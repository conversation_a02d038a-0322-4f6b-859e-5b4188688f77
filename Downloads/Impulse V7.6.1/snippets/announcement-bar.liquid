{%- assign show_announcement = false -%}
{%- assign announcement_block_count = 0 -%}
{%- for block in section.blocks -%}
  {%- if block.type == 'announcement' or block.type == '@app' -%}
    {%- assign show_announcement = true -%}
    {%- assign announcement_block_count = announcement_block_count | plus: 1 -%}
  {%- endif -%}
{%- endfor -%}

{% if show_announcement %}
  <div class="announcement-bar">
    <div class="page-width">
      <div class="slideshow-wrapper">
        <button type="button" class="visually-hidden slideshow__pause" data-id="{{ section.id }}" aria-live="polite">
          <span class="slideshow__pause-stop">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-pause" viewBox="0 0 10 13"><path d="M0 0h3v13H0zm7 0h3v13H7z" fill-rule="evenodd"/></svg>
            <span class="icon__fallback-text">{{ 'sections.slideshow.pause_slideshow' | t }}</span>
          </span>
          <span class="slideshow__pause-play">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-play" viewBox="18.24 17.35 24.52 28.3"><path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/></svg>
            <span class="icon__fallback-text">{{ 'sections.slideshow.play_slideshow' | t }}</span>
          </span>
        </button>

        <div
          id="AnnouncementSlider"
          class="announcement-slider"
          data-compact="{{ section.settings.announcement_compact }}"
          data-block-count="{{ announcement_block_count }}">
          {%- assign slide_index = 0 -%}
          {%- for block in section.blocks -%}
            {%- if block.type == '@app' -%}
              <div
                id="AnnouncementSlide-{{ block.id }}"
                class="announcement-slider__slide"
                data-index="{{ slide_index }}"
                {{ block.shopify_attributes }}
              >
                {%- render block -%}
              </div>
              {%- assign slide_index = slide_index | plus: 1 -%}
            {%- endif -%}
            {%- if block.type == 'announcement' -%}
              <div
                id="AnnouncementSlide-{{ block.id }}"
                class="announcement-slider__slide"
                data-index="{{ slide_index }}"
                {{ block.shopify_attributes }}
              >
                {%- if block.settings.link != blank -%}
                  <a class="announcement-link" href="{{ block.settings.link }}">
                {%- endif -%}
                  {%- if block.settings.text != blank -%}
                    <span class="announcement-text">{{ block.settings.text }}</span>
                  {%- endif -%}
                  {%- if block.settings.link_text != blank -%}
                    <span class="announcement-link-text">{{ block.settings.link_text }}</span>
                  {%- endif -%}
                {%- if block.settings.link != blank -%}
                  </a>
                {%- endif -%}
              </div>
              {%- assign slide_index = slide_index | plus: 1 -%}
            {%- endif -%}
          {%- endfor -%}
        </div>
      </div>
    </div>
  </div>

{% endif %}
