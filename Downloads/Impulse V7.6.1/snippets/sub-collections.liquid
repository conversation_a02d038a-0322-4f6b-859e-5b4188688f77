{%- liquid
  capture gridView
    if sub_collection_links.size < per_row
      assign products_per_row = sub_collection_links.size
    else
      assign products_per_row = per_row
    endif
    render 'products_per_row', products_per_row: products_per_row, style: 'fractions'
  endcapture
-%}

<div class="grid grid--uniform{% unless settings.collection_grid_gutter %} grid--no-gutters{% endunless %}">
  {%- for sub_collection_link in sub_collection_links -%}
    {%- if sub_collection_link.url contains '/collections/' -%}
      {%- liquid
        assign sub_collection_split = sub_collection_link.url | split: '/'
        assign sub_collection_handle = sub_collection_split | last
        assign sub_collection = collections[sub_collection_handle]
      -%}

      {%- if sub_collection != blank -%}
        {%- unless parent_url == sub_collection.url -%}
          {%- render 'collection-grid-item',
            collection: sub_collection,
            gridView: gridView,
            collection_title: sub_collection_link.title
            per_row: per_row,
            sizeVariable: gridView,
            fallback: 'variable',
          -%}
        {%- endunless -%}
      {%- endif -%}
    {%- endif -%}
  {%- endfor -%}
</div>

