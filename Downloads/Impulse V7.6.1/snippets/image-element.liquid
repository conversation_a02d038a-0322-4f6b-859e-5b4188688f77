{% comment %}
  Image element snippet
  - Builds out our theme images
    - Parameters:
     - img (Required - Image object)
     - classes (Optional - String)
     - alt (Optional - String)
     - loading (Optional - String)
     - img_height (Optional - String)
     - img_width (Optional - String)
     - sizes (Optional - String)
     - widths (Optional - String)
     - preload (Optional - Boolean)
     - itemprop (Optional - String)
     - format (Optional - String)
     - role (Optional - String)
     - aria-hidden (Optional - Boolean)
     - style (Optional - String)
  - Builds out image tags for hosted files
    - Parameters:
      - asset (Required - String url)
      - host (Optional - String), default is Shopify
      - type (Required - String)
      - classes (Optional - String)
      - alt (Optional - String)
      - widths (Optional - String)
      - data-name (Optional - String)
      - data-value (Optional - String)
      - width (Optional - Number)
      - height (Optional - Number)
  - Builds out images that connect into Photoswipe
    - Parameters:
      - img (Required - Media preview image)
      - type (Required - String)
      - product_zoom_size (Required - String)
      - loopIndex (Required - String)
      - media_height (Required - String)
      - media_width (Required - String)
      - media (Required - Media object)
      - classes (Optional - String)
      - alt (Optional - String)
      - widths (Optional - String)

  https://shopify.dev/api/liquid/filters#image_tag
  https://shopify.dev/api/liquid/filters#image_url
{% endcomment %}

{% liquid
  assign classes = classes | escape
  assign alt = alt | escape

  # Image Loading https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/loading#value
    # Lazyloading or eager
    # Eager is the default loading behavior of the browser, which is the same as not including the attribute and means the image is loaded as soon as possible
    # We are setting it explicitly here but there are no performance benefits

  if loading == 'eager'
    assign loading = 'eager'
  elsif loading == false
    assign loading = 'eager'
    assign preload = true
  else
    assign loading = 'lazy'
    assign preload = false
  endif

  # Image sizes https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/sizes
   # Can explicitly pass in sizes. For example: sizes: '200px' or '(min-width: 769px) 400px, 30vw'
   # Can pass in only a sizeVariable
    # This can represent a setting like products per row which means each grid item has a width that can vary
    # Can pass in an explicit value
   # Can also pass in an optional fallback size which is only used when the first condition passed to sizes is not true.
   # If sizes and sizeVariable is blank we output a default value for sizes which is 100vw
  if sizes == blank and sizeVariable != blank
    capture sizes
      render 'sizes-explicit', sizeVariable: sizeVariable, fallback: fallback
    endcapture
  endif

  if sizes == blank
    assign sizes = '100vw'
  else
    assign sizes = sizes
  endif

  if alt == blank
    assign alt = img.alt | default: ''
  endif

  if animation == blank
    assign animation = 'image-fade-in'
  else
    assign animation = animation
  endif

  # Add a class to the image so we can target theme images
  assign classes = classes | append: ' image-element'
%}

<image-element data-aos="{{ animation }}" data-aos-offset="150">

{% if type == blank %}
  {% if widths != blank %}
    {%- if img_width == blank -%}
      {%- assign img_width = widths | split: ',' | last | strip -%}
    {%- endif -%}
    {%- if img_tag_width == blank -%}
      {%- assign img_tag_width = img_width | strip -%}
    {%- endif -%}
    {%- if img_tag_height == blank -%}
      {%- assign img_tag_height = img_tag_width | divided_by: img.aspect_ratio -%}
    {%- endif -%}
    {{ img
      | image_url: width: img_width, format: format
      | image_tag: preload: preload,
                  height: img_tag_height,
                  width: img_tag_width,
                  class: classes,
                  loading: loading,
                  alt: alt,
                  sizes: sizes,
                  widths: widths,
                  itemprop: itemprop,
                  role: role,
                  aria-hidden: ariaHidden,
                  style: style
    }}
  {% else %}
    {%- if img_width == blank -%}
      {%- assign img_width = img.width | times: 2 | round -%}
    {%- endif -%}
    {%- if img_tag_width == blank -%}
      {%- assign img_tag_width = img_width -%}
    {%- endif -%}
    {%- if img_tag_height == blank -%}
      {%- assign img_tag_height = img_tag_width | divided_by: img.aspect_ratio -%}
    {%- endif -%}
    {{ img
      | image_url: width: img_width, format: format
      | image_tag: preload: preload,
                  height: img_tag_height,
                  width: img_tag_width,
                  class: classes,
                  loading: loading,
                  alt: alt,
                  sizes: sizes,
                  itemprop: itemprop,
                  role: role,
                  aria-hidden: ariaHidden,
                  style: style
    }}
  {% endif %}
{% elsif type == 'photoswipe' %}
  {% assign widthsArray = widths | split: ',' %}
  {%- capture img_widths -%}
    {% for width in widthsArray %}
      {{ media | image_url: width: width }} {{ width | append: 'w' }},
    {% endfor %}
  {%- endcapture -%}
  {% assign img_width = widthsArray | last | strip %}
  {% assign img_height = img_width | divided_by: img.aspect_ratio %}
  <img src="{{ img | image_url: width: img_width }}"
    width="{{ img_width }}"
    height="{{ img_height }}"
    class="{{ classes }}"
    loading="{{ loading }}"
    alt="{{ alt }}"
    srcset="{{ img_widths }}"
    data-photoswipe-src="{{ img | image_url: width: product_zoom_size, format: format }}"
    data-photoswipe-width="{{ media_width }}"
    data-photoswipe-height="{{ media_height }}"
    data-index="{{ loopIndex }}"
    sizes="{{ sizes }}"
  >
{% else %}
  {% if host == 'theme' %}
    {% assign img_src = asset | asset_url %}
  {% else %}
    {% assign img_src = asset | shopify_asset_url %}
  {% endif %}
  {%- capture img_widths -%}
    {% if host == 'theme' %}
      {{ asset | asset_img_url: '360x' }} 360w,
      {{ asset | asset_img_url: '540x' }} 540w,
      {{ asset | asset_img_url: '720x' }} 720w,
      {{ asset | asset_img_url: '900x' }} 900w,
      {{ asset | asset_img_url: '1080x' }} 1080w
    {% endif %}
  {%- endcapture -%}
  <img src="{{ img_src }}"
       width="{{ img_width }}"
       height="{{ img_height }}"
       class="{{ classes }}"
       loading="{{ loading }}"
       alt="{{ alt }}"
       {% if img_widths != blank %}srcset="{{ img_widths }}"{% endif %}
       {% if data-name and data-value %}data-{{ data-name }}="{{ data-value }}"{% endif %}
       sizes="{{ sizes }}"
  >
{% endif %}

</image-element>
