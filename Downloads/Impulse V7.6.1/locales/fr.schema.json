{"sections": {"header-group": {"name": "Groupe d'en-tête"}, "footer-group": {"name": "Groupe de pied de page"}, "popup-group": {"name": "Groupe contextuel"}, "advanced-content": {"name": "<PERSON><PERSON><PERSON>", "settings": {"full_width": {"label": "<PERSON>ur de la page entière"}, "space_around": {"label": "Ajouter un espacement au-dessus et en dessous"}}, "blocks": {"html": {"name": "HTML", "settings": {"code": {"label": "HTML", "info": "Prend en charge Liquid"}, "width": {"label": "<PERSON><PERSON>"}, "alignment": {"label": "Alignement vertical", "info": "S'aligne à côté d'autres contenus personnalisés", "options": {"top-middle": {"label": "<PERSON><PERSON>"}, "center": {"label": "Milieu"}, "bottom-middle": {"label": "Bas"}}}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}, "link": {"label": "<PERSON><PERSON>"}, "width": {"label": "<PERSON><PERSON>"}, "alignment": {"label": "Alignement vertical", "info": "S'aligne à côté d'autres contenus personnalisés", "options": {"top-middle": {"label": "<PERSON><PERSON>"}, "center": {"label": "Milieu"}, "bottom-middle": {"label": "Bas"}}}}}}, "presets": {"custom_content": {"name": "<PERSON><PERSON><PERSON>"}}}, "apps": {"name": "Applications", "settings": {"full_width": {"label": "<PERSON>ur de la page entière"}, "space_around": {"label": "Ajouter un espacement au-dessus et en dessous"}}, "presets": {"apps": {"name": "Applications"}}}, "article-template": {"name": "Pages de l'article", "settings": {"image_hero": {"label": "Utilisation de l'image vedette comme hero pleine largeur", "info": "(si l'image de l'article est définie)"}, "blog_show_tags": {"label": "Afficher les balises"}, "blog_show_date": {"label": "Affiche<PERSON> la date"}, "blog_show_comments": {"label": "Afficher le nombre de commentaires"}, "blog_show_author": {"label": "Afficher l'auteur"}, "social_sharing_blog": {"label": "Afficher les boutons de partage social"}}}, "background-image-text": {"name": "Grande image avec zone de texte", "settings": {"subtitle": {"label": "Sous-titre"}, "title": {"label": "Titre"}, "text": {"label": "Texte"}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "image": {"label": "Image"}, "focal_point": {"label": "Point focal de l'image", "info": "Utilisé pour garder le sujet de votre photo en vue.", "options": {"20_0": {"label": "En haut à gauche"}, "top": {"label": "<PERSON><PERSON>"}, "80_0": {"label": "En haut à droite"}, "20_50": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Milieu"}, "80_50": {"label": "<PERSON><PERSON><PERSON>"}, "20_100": {"label": "En bas à gauche"}, "bottom": {"label": "Bas"}, "80_100": {"label": "En bas à droite"}}}, "layout": {"label": "Mise en page", "options": {"left": {"label": "Texte à gauche"}, "right": {"label": "Texte à droite"}}}, "height": {"label": "Hauteur de la section"}, "framed": {"label": "Ajouter un cadre"}, "parallax_direction": {"label": "Direction de la parallaxe", "options": {"top": {"label": "Verticale"}, "left": {"label": "Horizontale"}}}, "parallax": {"label": "Activer la parallaxe"}}, "presets": {"large_image_with_text_box": {"name": "Grande image avec zone de texte"}}}, "background-video-text": {"name": "Grande vidéo avec zone de texte", "settings": {"subtitle": {"label": "Sous-titre"}, "title": {"label": "Titre"}, "text": {"label": "Texte"}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton", "info": "Les liens vers les vidéos YouTube s'ouvrent dans un lecteur vidéo"}, "video_url": {"label": "Lien vers une vidéo d'arrière-plan", "info": "Prend en charge YouTube, .MP4 et Vimeo. Toutes les fonctionnalités ne sont pas prises en charge par Vimeo. [En savoir plus](https://archetypethemes.co/blogs/impulse/how-do-i-add-background-videos)"}, "color_border": {"label": "Couleur de la vidéo", "info": "Utilisé pour la bordure mobile"}, "layout": {"label": "Mise en page", "options": {"left": {"label": "Texte à gauche"}, "right": {"label": "Texte à droite"}}}, "height": {"label": "Hauteur de la section"}}, "presets": {"large_video_with_text_box": {"name": "Grande vidéo avec zone de texte"}}}, "blog-posts": {"name": "Articles de blog", "settings": {"title": {"label": "Titre"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Messages"}, "blog_show_tags": {"label": "Afficher les balises"}, "blog_show_date": {"label": "Affiche<PERSON> la date"}, "blog_show_comments": {"label": "Afficher le nombre de commentaires"}, "blog_show_author": {"label": "Afficher l'auteur"}, "view_all": {"label": "<PERSON><PERSON><PERSON><PERSON> le bouton « Tout voir »"}, "blog_image_size": {"label": "<PERSON>lle de l'image", "options": {"natural": {"label": "Naturel"}, "square": {"label": "Carré (1:1)"}, "landscape": {"label": "Paysage (4:3)"}, "portrait": {"label": "Portrait (2:3)"}, "wide": {"label": "Large (16:9)"}}}, "divider": {"label": "Afficher le séparateur de section"}}, "presets": {"blog_posts": {"name": "Articles de blog"}}}, "blog-template": {"name": "Pages du blog", "settings": {"blog_show_tag_filter": {"label": "Aff<PERSON>r le filtre de tags"}, "blog_show_rss": {"label": "Afficher le lien RSS"}, "blog_show_tags": {"label": "Afficher les balises"}, "blog_show_date": {"label": "Affiche<PERSON> la date"}, "blog_show_comments": {"label": "Afficher le nombre de commentaires"}, "blog_show_author": {"label": "Afficher l'auteur"}, "blog_show_excerpt": {"label": "Afficher l'extrait"}, "blog_image_size": {"label": "<PERSON>lle de l'image", "options": {"natural": {"label": "Naturel"}, "square": {"label": "Carré (1:1)"}, "landscape": {"label": "Paysage (4:3)"}, "portrait": {"label": "Portrait (2:3)"}, "wide": {"label": "Large (16:9)"}}}}}, "collection-header": {"name": "En-tête de collection", "settings": {"enable": {"label": "<PERSON><PERSON> l'en<PERSON>tête"}, "collection_image_enable": {"label": "Afficher l'image de la collection"}, "parallax_direction": {"label": "Direction de la parallaxe", "options": {"top": {"label": "Verticale"}, "left": {"label": "Horizontale"}}}, "parallax": {"label": "Image parallaxe"}}}, "collection-return": {"name": "Retour à la collection"}, "contact-form": {"name": "Formulaire de contact", "settings": {"content": "Toutes les soumissions sont envoyées à l'adresse e-mail du client de votre magasin. [En savoir plus](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "title": {"label": "Titre"}, "text": {"label": "Texte"}, "show_phone": {"label": "Aff<PERSON>r le numéro de téléphone"}, "narrow_column": {"label": "Réduire la colonne"}}, "presets": {"contact_form": {"name": "Formulaire de contact"}}}, "faq": {"name": "FAQ", "settings": {"title": {"label": "Titre"}}, "blocks": {"rich_text": {"name": "Texte enrichi", "settings": {"title": {"label": "Titre"}, "text": {"label": "Texte"}, "align_text": {"label": "Alignement du texte", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "question": {"name": "Question", "settings": {"title": {"label": "Question"}, "text": {"label": "Texte"}}}}, "presets": {"faq": {"name": "FAQ"}}}, "featured-collection": {"name": "Collection en vedette", "settings": {"title": {"label": "Titre"}, "home_featured_products": {"label": "Collection"}, "per_row": {"label": "Produits par ligne"}, "rows": {"label": "Rangées de produits"}, "mobile_scrollable": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "view_all": {"label": "Afficher le lien « Tout voir »"}, "divider": {"label": "Afficher le séparateur de section"}}, "presets": {"featured_collection": {"name": "Collection en vedette"}}}, "featured-collections": {"name": "Liste des collections", "settings": {"title": {"label": "Titre"}, "divider": {"label": "Afficher le séparateur de section"}, "per_row": {"label": "Collections par rangée"}, "enable_gutter": {"label": "Ajouter un espacement"}}, "presets": {"collection_list": {"name": "Liste des collections"}}, "blocks": {"collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}, "title": {"label": "Titre"}, "focal_point": {"label": "Point focal", "info": "Utilisé pour garder le sujet de votre photo en vue.", "options": {"20_0": {"label": "En haut à gauche"}, "top_center": {"label": "En haut au centre"}, "80_0": {"label": "En haut à droite"}, "20_50": {"label": "G<PERSON><PERSON>"}, "center_center": {"label": "Centre"}, "80_50": {"label": "<PERSON><PERSON><PERSON>"}, "20_100": {"label": "En bas à gauche"}, "bottom_center": {"label": "En bas au centre"}, "80_100": {"label": "En bas à droite"}}}}}}}, "featured-product": {"name": "Produit en vedette", "settings": {"featured_product": {"label": "Produit"}, "divider": {"label": "Afficher le séparateur de section"}, "sku_enable": {"label": "Afficher l'UGS"}, "header_media": "Support multimédia", "content": "En savoir plus sur les [types de médias](https://help.shopify.com/en/manual/products/product-media)", "image_position": {"label": "Position", "options": {"left": {"label": "G<PERSON><PERSON>"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "image_size": {"label": "<PERSON><PERSON>", "options": {"small": {"label": "<PERSON>"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "large": {"label": "Grand"}}}, "product_zoom_enable": {"label": "<PERSON>z le zoom sur l'image"}, "thumbnail_position": {"label": "Position de la vignette", "options": {"beside": {"label": "À côté des médias"}, "below": {"label": "En dessous du média"}}}, "thumbnail_arrows": {"label": "Affichez les flèches des vignettes"}, "mobile_layout": {"label": "Mise en page sur mobile", "options": {"partial": {"label": "Largeur de 75 %"}, "full": {"label": "<PERSON><PERSON>e largeur"}}}, "enable_video_looping": {"label": "<PERSON><PERSON> le bouclage de la vidéo"}, "product_video_style": {"label": "Style de la vidéo", "options": {"muted": {"label": "<PERSON><PERSON><PERSON><PERSON> sans son"}, "unmuted": {"label": "Vid<PERSON>o avec du son"}}, "info": "La vidéo avec du son ne sera pas lue automatiquement"}}, "presets": {"featured_product": {"name": "Produit en vedette"}}}, "featured-video": {"name": "Vidéo", "settings": {"title": {"label": "Titre"}, "video_url": {"label": "<PERSON>n vid<PERSON>o"}, "divider": {"label": "Afficher le séparateur de section"}}, "presets": {"video": {"name": "Vidéo"}}}, "footer-promotions": {"name": "Promotions en bas de page", "settings": {"hide_homepage": {"label": "Ne pas afficher sur la page d'accueil"}, "image_size": {"label": "<PERSON>lle de l'image", "options": {"natural": {"label": "Naturel"}, "square": {"label": "Carré (1:1)"}, "landscape": {"label": "Paysage (4:3)"}, "portrait": {"label": "Portrait (2:3)"}, "wide": {"label": "Large (16:9)"}}}}, "blocks": {"column": {"name": "Colonne", "settings": {"enable_image": {"label": "Afficher l'image"}, "image": {"label": "Image"}, "title": {"label": "Titre"}, "text": {"label": "Texte"}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON><PERSON>"}}}}}, "footer": {"name": "Pied de page", "settings": {"header_language_selector": "Pour ajouter une langue, allez à vos [paramètres de langue.](/admin/settings/languages)", "show_locale_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de langue"}, "header_currency_selector": "Pour ajouter une devise, accédez à vos [paramètres de devise](/admin/settings/payments).", "show_currency_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de devise"}, "show_currency_flags": {"label": "Afficher les drapeaux des devises"}, "header_additional_footer_content": "Contenu supplémentaire du pied de page", "show_payment_icons": {"label": "Afficher les icônes de paiement"}, "show_copyright": {"label": "<PERSON><PERSON><PERSON><PERSON> le droit d'auteur"}, "copyright_text": {"label": "Texte de droit d'auteur supplémentaire"}}, "blocks": {"logo": {"name": "Logo", "settings": {"logo": {"label": "Image du logo"}, "desktop_logo_height": {"label": "Hauteur du logo"}, "container_width": {"label": "<PERSON>ur de la colonne"}}}, "navigation": {"name": "Navigation", "settings": {"show_footer_title": {"label": "<PERSON><PERSON><PERSON><PERSON> le titre"}, "menu": {"label": "Choisissez un menu", "info": "Ce menu n'affichera pas les éléments déroulants"}, "container_width": {"label": "<PERSON>ur de la colonne"}}}, "newsletter_and_social": {"name": "Newsletter et social", "settings": {"show_footer_title": {"label": "<PERSON><PERSON><PERSON><PERSON> le titre"}, "content": "Tous les clients qui s'inscrivent auront un compte créé pour eux dans Shopify. [Consultez les clients](/admin/customers).", "title": {"label": "Titre"}, "text": {"label": "Texte", "info": "Optionnel"}, "container_width": {"label": "<PERSON>ur de la colonne"}}}, "custom_text": {"name": "Texte personnalis<PERSON>", "settings": {"show_footer_title": {"label": "<PERSON><PERSON><PERSON><PERSON> le titre"}, "title": {"label": "Titre"}, "image": {"label": "Image"}, "text": {"label": "Texte"}, "container_width": {"label": "<PERSON>ur de la colonne"}}}}}, "giftcard-header": {"name": "<PERSON>-tête", "settings": {"logo": {"label": "Logo"}, "desktop_logo_width": {"label": "Largeur du logo pour ordinateur de bureau"}, "mobile_logo_width": {"label": "Largeur du logo pour mobile", "info": "Défini comme une largeur maximale, peut apparaître plus petit"}}}, "header": {"name": "<PERSON>-tête", "settings": {"main_menu_link_list": {"label": "Navigation"}, "hover_menu": {"label": "Activer la liste déroulante au survol"}, "mega_menu_images": {"label": "Afficher les images du méga-menu", "info": "[Comment créer un méga-menu](https://archetypethemes.co/blogs/impulse/how-do-i-create-a-mega-menu)"}, "main_menu_alignment": {"label": "Disposition de l'en-tête", "options": {"left": {"label": "Logo à gauche, menu à gauche"}, "left-center": {"label": "Logo à gauche, menu au centre"}, "left-drawer": {"label": "Logo à gauche, menu déroulant"}, "center-left": {"label": "Logo au centre, menu à gauche"}, "center-split": {"label": "Logo au centre, menu séparé"}, "center": {"label": "Logo au centre, menu en dessous"}, "center-drawer": {"label": "Centre du logo, tiroir du menu"}}}, "header_style": {"label": "Style de l'en-tête", "options": {"normal": {"label": "Normal"}, "sticky": {"label": "<PERSON>lant"}}}, "sticky_index": {"label": "En-tête superposé sur la page d'accueil"}, "sticky_collection": {"label": "En-tête superposé sur la collection", "info": "(si l'image de la collection est activée)"}, "header_announcement_bar": "Barre d'annonces", "announcement_compact": {"label": "Utiliser le style compact"}, "announcement_above_header": {"label": "Toujours afficher au-dessus de l'en-tête"}, "header_toolbar": "<PERSON>e d’outils", "toolbar_menu": {"label": "Navigation", "info": "Ce menu n'affichera pas les éléments déroulants"}, "toolbar_social": {"label": "Afficher les icônes sociales"}, "header_language_selector": "Pour ajouter une langue, allez à vos [paramètres de langue.](/admin/settings/languages)", "show_locale_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de langue"}, "header_currency_selector": "Pour ajouter une devise, accédez à vos [paramètres de devise](/admin/settings/payments).", "show_currency_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de devise"}, "show_currency_flags": {"label": "Afficher les drapeaux des devises"}}, "blocks": {"logo": {"name": "Logo", "settings": {"logo": {"label": "Logo"}, "logo-inverted": {"label": "Logo blanc", "info": "U<PERSON><PERSON><PERSON> lorsqu'il se trouve au-dessus d'une image"}, "desktop_logo_width": {"label": "Largeur du logo pour ordinateur de bureau"}, "mobile_logo_width": {"label": "Largeur du logo pour mobile", "info": "Défini comme une largeur maximale, peut apparaître plus petit"}}}, "announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "Titre"}, "link_text": {"label": "Texte"}, "link": {"label": "<PERSON><PERSON>"}}}}}, "hero-video": {"name": "<PERSON> vidéo", "settings": {"title": {"label": "Titre"}, "title_size": {"label": "Taille du texte de l'en-tête"}, "subheading": {"label": "Sous-titre"}, "link_text": {"label": "Texte du bouton"}, "link": {"label": "<PERSON>n du bouton", "info": "Les liens vers les vidéos YouTube s'ouvrent dans un lecteur vidéo"}, "color_accent": {"label": "Boutons"}, "text_align": {"label": "Alignement du texte", "options": {"vertical-center_horizontal-left": {"label": "Centre gauche"}, "vertical-center_horizontal-center": {"label": "Centre"}, "vertical-center_horizontal-right": {"label": "Centre droite"}, "vertical-bottom_horizontal-left": {"label": "En bas à gauche"}, "vertical-bottom_horizontal-center": {"label": "En bas au centre"}, "vertical-bottom_horizontal-right": {"label": "En bas à droite"}}}, "video_url": {"label": "Lien vers une vidéo d'arrière-plan", "info": "Prend en charge YouTube, .MP4 et Vimeo. Toutes les fonctionnalités ne sont pas prises en charge par Vimeo. [En savoir plus](https://archetypethemes.co/blogs/impulse/how-do-i-add-background-videos)"}, "overlay_opacity": {"label": "Protection du texte", "info": "Assombrit votre image pour garantir la lisibilité de votre texte"}, "section_height": {"label": "Hauteur du bureau", "options": {"450px": {"label": "450px"}, "550px": {"label": "550px"}, "650px": {"label": "650px"}, "750px": {"label": "750px"}, "100vh": {"label": "Plein écran"}}}, "mobile_height": {"label": "<PERSON>ur du mobile", "options": {"auto": {"label": "Auto"}, "250px": {"label": "250px"}, "300px": {"label": "300px"}, "400px": {"label": "400px"}, "500px": {"label": "500px"}, "100vh": {"label": "Plein écran"}}}}, "presets": {"video_hero": {"name": "<PERSON> vidéo"}}}, "image-comparison": {"name": "Comparaison d'images", "settings": {"heading": {"label": "Titre"}, "heading_size": {"label": "<PERSON><PERSON> du titre", "options": {"large": {"label": "Grande"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "small": {"label": "Petite"}}}, "heading_position": {"label": "Position du titre", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centre"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "fullwidth": {"label": "<PERSON>ur de la page entière"}, "slider_style": {"label": "Style de curseur", "options": {"classic": {"label": "Classique"}, "minimal": {"label": "Minimale"}}}, "height": {"label": "<PERSON><PERSON>"}, "header_colors": "Coleurs", "color": {"label": "Bouton"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}}}}}, "list-collections-template": {"name": "Page de liste des collections", "settings": {"title_enable": {"label": "<PERSON><PERSON><PERSON><PERSON> le titre"}, "content": "Toutes vos collections sont listées par défaut. Pour personnaliser votre liste, choisissez « Sélectionné » et ajoutez des collections.", "display_type": {"label": "Sélectionnez les collections à afficher", "options": {"all": {"label": "<PERSON>ut"}, "selected": {"label": "Sélectionné"}}}, "sort": {"label": "Trier les collections par :", "info": "Le tri s'applique uniquement lorsque « Tout » est sélectionné.", "options": {"products_high": {"label": "Nombre de produits, par ordre décroissant"}, "products_low": {"label": "Nombre de produits, par ordre croissant"}, "alphabetical": {"label": "Alphabétique, de A à Z"}, "alphabetical_reversed": {"label": "Alphabétique, de Z à A"}, "date": {"label": "Date, de la plus ancienne à la plus récente"}, "date_reversed": {"label": "Date, de la plus récente à la plus ancienne"}}}, "grid": {"label": "Collections par rangée"}}, "blocks": {"collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}}, "marquee": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "Texte"}, "link": {"label": "<PERSON><PERSON>"}, "text_size": {"label": "<PERSON>lle du texte"}, "text_spacing": {"label": "Ajouter un espacement"}, "color_scheme": {"label": "Combinaison de couleurs", "options": {"button": {"label": "Bouton"}, "text": {"label": "Texte"}}}, "direction": {"label": "Direction", "options": {"left": {"label": "G<PERSON><PERSON>"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "speed": {"label": "Vitesse", "options": {"fast": {"label": "Rapide"}, "normal": {"label": "Normal"}, "slow": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"scrolling_text": {"name": "<PERSON><PERSON>"}}}, "hotspots": {"name": "Points chauds des images", "settings": {"title": {"label": "Titre"}, "heading_size": {"label": "<PERSON><PERSON> du titre", "options": {"large": {"label": "Grande"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "small": {"label": "Petite"}}}, "heading_position": {"label": "Position du titre", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centre"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "image": {"label": "Image", "info": "A recommandé un format carré pour une expérience mobile optimale"}, "indent_image": {"label": "Pleine largeur de page"}, "image_position": {"label": "Position de l'image", "options": {"left": {"label": "G<PERSON><PERSON>"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "hotspot_style": {"label": "Style des points chauds", "options": {"dot": {"label": "Point"}, "plus": {"label": "Plus"}, "bag": {"label": "Sac"}, "tag": {"label": "Étiquette"}}}, "hotspot_color": {"label": "Couleur des points chauds"}}, "blocks": {"product": {"name": "Points chauds des produits", "settings": {"featured_product": {"label": "Produit"}, "vertical": {"label": "Position verticale"}, "horizontal": {"label": "Position horizontale"}}}, "paragraph": {"name": "Points chauds du paragraphe", "settings": {"subheading": {"label": "Sous-titre"}, "heading": {"label": "Titre"}, "content": {"label": "Contenu"}, "button_text": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "vertical": {"label": "Position verticale"}, "horizontal": {"label": "Position horizontale"}}}}}, "logo-list": {"name": "Liste des logos", "settings": {"title": {"label": "Titre"}, "logo_opacity": {"label": "Opacité du logo"}, "divider": {"label": "Afficher le séparateur de section"}}, "blocks": {"logo": {"name": "Logo", "settings": {"image": {"label": "Image"}, "link": {"label": "<PERSON><PERSON>", "info": "Optionnel"}}}}, "presets": {"logo_list": {"name": "Liste des logos"}}}, "main-404": {"name": "Page 404"}, "main-cart": {"name": "Page du panier"}, "main-collection": {"name": "Grille de produit", "settings": {"header_filtering_and_sorting": "Filtrage et tri", "enable_sidebar": {"label": "<PERSON><PERSON> le filtre", "info": "Permettez à vos clients de filtrer les collections et les résultats de recherche en fonction de la disponibilité des produits, du prix, de la couleur, etc. [Personnalisez les filtres](/admin/menus)"}, "collapsed": {"label": "Réduire les filtres"}, "filter_style": {"label": "Style de filtre", "options": {"sidebar": {"label": "Barre la<PERSON>"}, "drawer": {"label": "Tiroir"}}}, "enable_color_swatches": {"label": "Activer les échantillons de couleur", "info": "[Consultez les instructions de configuration](https://archetypethemes.co/blogs/impulse/how-do-i-set-up-color-swatches)"}, "enable_swatch_labels": {"label": "Afficher les étiquettes des échantillons"}, "enable_sort": {"label": "Afficher les options de tri"}}, "blocks": {"collection_description": {"name": "Description de la collection"}, "products": {"name": "Produits", "settings": {"enable_collection_count": {"label": "Activer le comptage des collections"}, "per_row": {"label": "Produits par ligne"}, "rows_per_page": {"label": "Lignes par page"}, "mobile_flush_grid": {"label": "Grille affleurante sur mobile"}}}, "subcollections": {"name": "Sous-collections", "settings": {"content": "Les liens vers les collections de votre menu apparaîtront ici. [En savoir plus](https://archetypethemes.co/blogs/impulse/how-do-i-create-subcollections)", "subcollections_per_row": {"label": "Sous-collections par ligne"}}}}}, "main-page-full-width": {"name": "Page (pleine largeur)"}, "main-page": {"name": "Page"}, "main-product": {"name": "Produit", "settings": {"sku_enable": {"label": "Afficher l'UGS"}, "header_media": "Support multimédia", "content": "En savoir plus sur les [types de médias](https://help.shopify.com/en/manual/products/product-media)", "image_position": {"label": "Position", "options": {"left": {"label": "G<PERSON><PERSON>"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "image_size": {"label": "<PERSON><PERSON>", "options": {"small": {"label": "<PERSON>"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "large": {"label": "Grand"}}}, "product_zoom_enable": {"label": "<PERSON>z le zoom sur l'image"}, "thumbnail_position": {"label": "Position de la vignette", "options": {"beside": {"label": "À côté des médias"}, "below": {"label": "En dessous du média"}}}, "thumbnail_height": {"label": "<PERSON><PERSON> de la vignette", "info": "S'applique uniquement lorsque la position de la vignette est définie sur « À côté des médias ».", "options": {"fixed": {"label": "Fixe"}, "flexible": {"label": "Flexible"}}}, "thumbnail_arrows": {"label": "Affichez les flèches des vignettes"}, "mobile_layout": {"label": "Mise en page sur mobile", "options": {"partial": {"label": "Largeur de 75 %"}, "full": {"label": "<PERSON><PERSON>e largeur"}}}, "enable_video_looping": {"label": "<PERSON><PERSON> le bouclage de la vidéo"}, "product_video_style": {"label": "Style de la vidéo", "options": {"muted": {"label": "<PERSON><PERSON><PERSON><PERSON> sans son"}, "unmuted": {"label": "Vid<PERSON>o avec du son"}}, "info": "La vidéo avec du son ne sera pas lue automatiquement"}}}, "main-search": {"name": "Recherche", "settings": {"header_filtering_and_sorting": "Filtrage et tri", "enable_sidebar": {"label": "<PERSON><PERSON> le filtre", "info": "Permettez à vos clients de filtrer les collections et les résultats de recherche en fonction de la disponibilité des produits, du prix, de la couleur, etc. [Personnalisez les filtres](/admin/menus)"}, "collapsed": {"label": "Réduire les filtres"}, "filter_style": {"label": "Style de filtre", "options": {"sidebar": {"label": "Barre la<PERSON>"}, "drawer": {"label": "Tiroir"}}}, "enable_color_swatches": {"label": "Activer les échantillons de couleur", "info": "[Consultez les instructions de configuration](https://archetypethemes.co/blogs/impulse/how-do-i-set-up-color-swatches)"}, "enable_swatch_labels": {"label": "Afficher les étiquettes des échantillons"}, "per_row": {"label": "Produits par ligne"}, "rows_per_page": {"label": "Lignes par page"}, "mobile_flush_grid": {"label": "Grille affleurante sur mobile"}}}, "map": {"name": "<PERSON><PERSON>", "settings": {"map_title": {"label": "Titre"}, "address": {"label": "Adresse et horaires"}, "map_address": {"label": "<PERSON>ress<PERSON> du plan", "info": "Google maps trouvera l'emplacement exact"}, "api_key": {"label": "Clé API Google Maps", "info": "<PERSON><PERSON> <PERSON> [enregistrer une clé API Google Maps](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key) pour afficher la carte."}, "show_button": {"label": "<PERSON><PERSON><PERSON><PERSON> le bouton « Obtenir un itinéraire »."}, "background_image": {"label": "Image", "info": "À utiliser à la place d'une clé API"}, "background_image_position": {"label": "Point focal de l'image", "options": {"top_left": {"label": "En haut à gauche"}, "top_center": {"label": "En haut au centre"}, "top_right": {"label": "En haut à droite"}, "center_left": {"label": "Centré à gauche"}, "center_center": {"label": "Centré au milieu"}, "center_right": {"label": "Au milieu à droite"}, "bottom_left": {"label": "En bas à gauche"}, "bottom_center": {"label": "En bas au centre"}, "bottom_right": {"label": "En bas à droite"}}, "info": "Utilisé pour garder le sujet de votre photo en vue."}}, "presets": {"map": {"name": "<PERSON><PERSON>"}}}, "newsletter-popup": {"name": "<PERSON><PERSON><PERSON> popup", "settings": {"mode": {"label": "<PERSON><PERSON> la fenêtre popup", "info": "Apparaît dans l'éditeur de thème lorsqu'il est désactivé."}, "disable_for_account_holders": {"label": "Désactiver pour les titulaires de compte", "info": "Ne sera pas visible par les clients qui ont créé un compte sur votre boutique."}, "popup_seconds": {"label": "<PERSON><PERSON><PERSON>", "info": "Le délai est désactivé dans l'éditeur de thème pour des raisons de visibilité"}, "popup_days": {"label": "<PERSON><PERSON><PERSON>", "info": "Nombre de jours avant qu'une fenêtre contextuelle rejetée ne réapparaisse."}, "header_content": "Contenu", "popup_title": {"label": "Titre"}, "popup_image": {"label": "Image", "info": "N'apparaît pas sur les téléphones mobiles afin de respecter les [directives interstitielles de Google] (https://developers.google.com/search/blog/2016/08/helping-users-easily-access-content-on) pour un meilleur référencement."}, "image_position": {"label": "Position de l'image", "options": {"left": {"label": "G<PERSON><PERSON>"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "popup_text": {"label": "Texte"}, "header_newsletter": "Bulletin d'information", "content": "Les clients qui s'inscrivent verront leur adresse e-mail ajoutée à la [liste des clients] qui acceptent le marketing (/admin/customers?query=&accepts_marketing=1).", "enable_newsletter": {"label": "Activer le bulletin d'information"}, "header_button": "Bouton", "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}}, "blocks": {"header": {"name": "Rappel autocollant", "settings": {"text": {"label": "Étiquette de rappel", "info": "Apparaît lorsque la fenêtre popup de la newsletter est fermée.", "default": "Obtenez 10 % de réduction"}}}}}, "newsletter": {"name": "Inscription à la liste de diffusion", "blocks": {"title": {"name": "Titre", "settings": {"title": {"label": "Titre"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Sous-titre"}}}, "form": {"name": "Formulaire"}, "share_buttons": {"name": "Boutons de partage"}}, "settings": {"content": "Les clients qui s'inscrivent verront leur adresse e-mail ajoutée à la [liste des clients] qui acceptent le marketing (/admin/customers?query=&accepts_marketing=1).", "color_scheme": {"label": "Combinaison de couleurs", "options": {"none": {"label": "Aucun"}}}, "color_background": {"label": "Arrière-plan"}, "color_text": {"label": "Texte"}, "heading_size": {"label": "<PERSON><PERSON> du titre", "options": {"extra_large": {"label": "Extra-grande"}, "large": {"label": "Grande"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "small": {"label": "Petite"}}}, "divider": {"label": "<PERSON><PERSON><PERSON><PERSON> le séparateur"}, "top_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> le remplissage supérieur"}, "bottom_padding": {"label": "Aff<PERSON>r le remplissage inférieur"}, "image_position": {"label": "Position de l'image", "options": {"left": {"label": "G<PERSON><PERSON>"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "image_width": {"label": "Largeur de l'image", "options": {"large": {"label": "Grande"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "small": {"label": "Petite"}}}, "image": {"label": "Image", "info": "Ajoutez du texte alt pour un meilleur référencement grâce au bouton « Modifier » ci-dessus."}, "align_text": {"label": "<PERSON><PERSON><PERSON> le texte", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centre"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"email_signup": {"name": "Inscription à la liste de diffusion"}}}, "password-header": {"name": "<PERSON>-tête", "settings": {"overlay_header": {"label": "En-tê<PERSON> de la superposition"}, "logo": {"label": "Image du logo"}, "desktop_logo_height": {"label": "Hauteur du logo pour ordinateur de bureau"}, "mobile_logo_height": {"label": "Hauteur du logo pour mobile"}}}, "product-full-width": {"name": "<PERSON>é<PERSON> en pleine largeur", "settings": {"content": "Pour les lignes de produits avec de longues descriptions, nous vous recommandons de placer votre description et vos onglets dans cette section.", "max_width": {"label": "Optimiser pour la lisibilité", "info": "Applique une largeur maximale"}}, "blocks": {"description": {"name": "Description", "settings": {"is_tab": {"label": "Afficher sous forme d'onglet"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte"}}}, "tab": {"name": "Onglet", "settings": {"title": {"label": "Titre"}, "content": {"label": "Contenu de l'onglet"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> le contenu de la page"}}}, "share_on_social": {"name": "Partager sur les réseaux sociaux", "settings": {"content": "Choisissez les plates-formes à partager dans les paramètres globaux du thème."}}, "separator": {"name": "Séparateur"}, "contact_form": {"name": "Formulaire de contact", "settings": {"content": "Toutes les soumissions sont envoyées à l'adresse e-mail du client de votre magasin. [En savoir plus](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "title": {"label": "Titre"}, "phone": {"label": "Ajouter un champ de numéro de téléphone"}}}, "html": {"name": "HTML", "settings": {"code": {"label": "HTML", "info": "Prend en charge Liquid"}}}}}, "product-complementary": {"name": "Produits complémentaires", "settings": {"paragraph": {"content": "Pour sélectionner des produits complémentaires, ajoutez l'application Search & Discovery. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations#complementary-products)"}, "product_complementary_heading": {"label": "<PERSON>-tête"}, "complementary_count": {"label": "Max produits à afficher"}, "per_slide": {"label": "Nombre de produits par slide"}, "control_type": {"label": "Type de pagination", "options": {"arrows": {"label": "Flèches"}, "dots": {"label": "Points"}}}, "header": {"content": "Fiche produit"}, "image_style": {"label": "Style d'image", "options": {"default": {"label": "Standard"}, "circle": {"label": "Cercle"}}}}}, "product-recommendations": {"name": "Produits connexes", "settings": {"show_product_recommendations": {"label": "Afficher des recommandations dynamiques", "info": "Les recommandations dynamiques utilisent les informations sur les commandes et les produits pour changer et s'améliorer au fil du temps. [En savoir plus](https://help.shopify.com/themes/development/recommended-products)"}, "product_recommendations_heading": {"label": "Titre"}, "related_count": {"label": "Nombre de produits connexes"}, "products_per_row": {"label": "Produits sur l'ordinateur de bureau par ligne"}}}, "promo-grid": {"name": "Grille promotionnelle", "settings": {"full_width": {"label": "<PERSON>ur de la page entière"}, "gutter_size": {"label": "Espacement"}, "space_above": {"label": "Ajouter un espacement supérieur"}, "space_below": {"label": "Ajouter l'espacement inférieur"}}, "presets": {"promotional_grid": {"name": "Grille promotionnelle"}}, "blocks": {"advanced": {"name": "<PERSON><PERSON><PERSON>", "settings": {"subheading": {"label": "Sous-titre"}, "heading": {"label": "Titre"}, "textarea": {"label": "Texte"}, "cta_text1": {"label": "Texte du bouton 1"}, "cta_link1": {"label": "Lien du bouton 1"}, "cta_text2": {"label": "Texte du bouton 2"}, "cta_link2": {"label": "Lien du bouton 2"}, "image": {"label": "Image"}, "video_url": {"label": "URL de la vidéo"}, "header_layout": "Mise en page", "width": {"label": "<PERSON><PERSON>"}, "height": {"label": "<PERSON><PERSON>"}, "text_size": {"label": "<PERSON>lle du texte"}, "header_alignment": "Alignement", "text_align": {"label": "Alignement du texte", "options": {"vertical-top_horizontal-left": {"label": "En haut à gauche"}, "vertical-top_horizontal-center": {"label": "En haut au centre"}, "vertical-top_horizontal-right": {"label": "En haut à droite"}, "vertical-center_horizontal-left": {"label": "Centre gauche"}, "vertical-center_horizontal-center": {"label": "Centre"}, "vertical-center_horizontal-right": {"label": "Centre droite"}, "vertical-bottom_horizontal-left": {"label": "En bas à gauche"}, "vertical-bottom_horizontal-center": {"label": "En bas au centre"}, "vertical-bottom_horizontal-right": {"label": "En bas à droite"}}}, "focal_point": {"label": "Point focal de l'image", "options": {"20_0": {"label": "En haut à gauche"}, "top": {"label": "En haut au centre"}, "80_0": {"label": "En haut à droite"}, "20_50": {"label": "Centre gauche"}, "center": {"label": "Centre"}, "80_50": {"label": "Centre droite"}, "20_100": {"label": "En bas à gauche"}, "bottom": {"label": "En bas au centre"}, "80_100": {"label": "En bas à droite"}}}, "header_design": "Design", "color_accent": {"label": "Boutons"}, "boxed": {"label": "A<PERSON>ter une boîte"}, "framed": {"label": "Ajouter un cadre"}}}, "banner": {"name": "Bannière", "settings": {"heading": {"label": "Titre"}, "text": {"label": "Texte"}, "link": {"label": "<PERSON><PERSON>"}, "label": {"label": "Étiquette de lien"}, "image": {"label": "Image"}, "header_design": "Design", "color_tint": {"label": "Teinte"}, "color_tint_opacity": {"label": "<PERSON><PERSON>"}, "framed": {"label": "Ajouter un cadre"}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}, "link": {"label": "<PERSON><PERSON>"}, "width": {"label": "<PERSON><PERSON>"}}}, "product": {"name": "Produit", "settings": {"product": {"label": "Produit"}, "subheading": {"label": "Sous-titre"}, "heading": {"label": "Titre"}, "textarea": {"label": "Texte"}, "link_label": {"label": "Texte du bouton"}, "label": {"label": "Étiquette"}, "enable_price": {"label": "<PERSON>ff<PERSON>r le prix"}, "width": {"label": "<PERSON><PERSON>"}, "text_size": {"label": "<PERSON>lle du texte"}, "header_design": "Design", "color_tint": {"label": "Teinte"}, "color_tint_opacity": {"label": "<PERSON><PERSON>"}, "framed": {"label": "Ajouter un cadre"}}}, "sale_collection": {"name": "Collection de vente", "settings": {"sale_collection": {"label": "Collection de vente"}, "top_text": {"label": "Texte du haut"}, "middle_text": {"label": "Texte du milieu"}, "bottom_text": {"label": "Texte du bas"}, "header_layout": "Mise en page", "width": {"label": "<PERSON><PERSON>"}, "header_design": "Design", "color_tint": {"label": "Teinte"}, "color_tint_opacity": {"label": "<PERSON><PERSON>"}, "boxed": {"label": "A<PERSON>ter une boîte"}, "framed": {"label": "Ajouter un cadre"}}}, "simple": {"name": "Simple", "settings": {"link": {"label": "<PERSON><PERSON>"}, "text": {"label": "Texte"}, "image": {"label": "Image"}, "header_layout": "Mise en page", "width": {"label": "<PERSON><PERSON>"}, "height": {"label": "<PERSON><PERSON>"}, "header_design": "Design", "color_tint": {"label": "Teinte"}, "color_tint_opacity": {"label": "<PERSON><PERSON>"}, "framed": {"label": "Ajouter un cadre"}}}}}, "recently-viewed": {"name": "Produits récemment consultés", "settings": {"content": "Les produits récemment consultés sont uniquement visibles lorsque vous naviguez en dehors de l'éditeur.", "recent_count": {"label": "Nombre de produits récents"}}}, "rich-text": {"name": "Texte enrichi", "settings": {"align_text": {"label": "Alignement du texte", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "narrow_column": {"label": "Réduire la colonne"}, "divider": {"label": "Afficher le séparateur de section"}}, "blocks": {"heading": {"name": "Titre", "settings": {"title": {"label": "Titre"}}}, "text": {"name": "Texte", "settings": {"enlarge_text": {"label": "<PERSON><PERSON><PERSON><PERSON> le texte"}, "text": {"label": "Texte"}}}, "button": {"name": "Bouton", "settings": {"link": {"label": "<PERSON>n du bouton"}, "link_text": {"label": "Texte du bouton"}}}, "page": {"name": "Page", "settings": {"page_text": {"label": "Page"}}}}, "presets": {"rich_text": {"name": "Texte enrichi"}}}, "slideshow": {"name": "Hero (diaporama optionnel)", "settings": {"section_height": {"label": "Hauteur du bureau", "options": {"natural": {"label": "Naturel"}, "450px": {"label": "450px"}, "550px": {"label": "550px"}, "650px": {"label": "650px"}, "750px": {"label": "750px"}, "100vh": {"label": "Plein écran"}}}, "mobile_height": {"label": "<PERSON>ur du mobile", "options": {"auto": {"label": "Auto"}, "250px": {"label": "250px"}, "300px": {"label": "300px"}, "400px": {"label": "400px"}, "500px": {"label": "500px"}, "100vh": {"label": "Plein écran"}}}, "parallax_direction": {"label": "Direction de la parallaxe", "options": {"top": {"label": "Verticale"}, "left": {"label": "Horizontale"}}}, "parallax": {"label": "Activer la parallaxe"}, "style": {"label": "Style de navigation des diapositives", "options": {"minimal": {"label": "Minimal"}, "arrows": {"label": "Flèches"}, "bars": {"label": "<PERSON><PERSON>"}, "dots": {"label": "Points"}}}, "autoplay": {"label": "Changement automatique des diapositives"}, "autoplay_speed": {"label": "Changer d'image à chaque fois"}}, "blocks": {"slide": {"name": "Diapositive", "settings": {"top_subheading": {"label": "Sous-titre"}, "title": {"label": "Titre"}, "title_size": {"label": "Taille du texte de l'en-tête"}, "subheading": {"label": "Texte"}, "link": {"label": "<PERSON><PERSON> de la diapositive"}, "link_text": {"label": "Texte du lien de la diapositive"}, "link_2": {"label": "Lien de la diapositive 2"}, "link_text_2": {"label": "Texte du lien de la diapositive 2"}, "color_accent": {"label": "Boutons"}, "text_align": {"label": "Alignement du texte", "options": {"vertical-center_horizontal-left": {"label": "Centre gauche"}, "vertical-center_horizontal-center": {"label": "Centre"}, "vertical-center_horizontal-right": {"label": "Centre droite"}, "vertical-bottom_horizontal-left": {"label": "En bas à gauche"}, "vertical-bottom_horizontal-center": {"label": "En bas au centre"}, "vertical-bottom_horizontal-right": {"label": "En bas à droite"}}}, "image": {"label": "Image"}, "image_mobile": {"label": "Image mobile"}, "overlay_opacity": {"label": "Protection du texte", "info": "Assombrit votre image pour garantir la lisibilité de votre texte"}, "focal_point": {"label": "Point focal de l'image", "info": "Utilisé pour garder le sujet de votre photo en vue.", "options": {"20_0": {"label": "En haut à gauche"}, "top_center": {"label": "En haut au centre"}, "80_0": {"label": "En haut à droite"}, "20_50": {"label": "G<PERSON><PERSON>"}, "center_center": {"label": "Centre"}, "80_50": {"label": "<PERSON><PERSON><PERSON>"}, "20_100": {"label": "En bas à gauche"}, "bottom_center": {"label": "En bas au centre"}, "80_100": {"label": "En bas à droite"}}}}}}, "presets": {"hero_optional_slideshow": {"name": "Hero (diaporama optionnel)"}}}, "testimonials": {"name": "Témoignages", "settings": {"title": {"label": "Titre"}, "align_text": {"label": "Alignement du texte", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "round_images": {"label": "Images circulaires", "info": "Requiert des images carrées"}, "color_background": {"label": "Arrière-plan"}, "color_text": {"label": "Texte"}}, "blocks": {"testimonial": {"name": "Témoignage", "settings": {"icon": {"label": "Icône", "options": {"none": {"label": "Aucun"}, "quote": {"label": "Citations"}, "5-stars": {"label": "5 étoiles"}, "4-stars": {"label": "4 étoiles"}, "3-stars": {"label": "3 étoiles"}, "2-stars": {"label": "2 étoiles"}, "1-star": {"label": "1 étoile"}}}, "testimonial": {"label": "Texte"}, "image": {"label": "Image de l'auteur"}, "author": {"label": "<PERSON><PERSON><PERSON>"}, "author_info": {"label": "Infos sur l'auteur"}}}}, "presets": {"testimonials": {"name": "Témoignages"}}}, "text-and-image": {"name": "Image avec texte", "settings": {"image": {"label": "Image"}, "image2": {"label": "Image 2"}, "subtitle": {"label": "Sous-titre"}, "title": {"label": "Titre"}, "text": {"label": "Texte"}, "image2_mask": {"label": "Forme de l'image 2"}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "button_style": {"label": "Style du bouton", "options": {"primary": {"label": "Primaire"}, "secondary": {"label": "Secondaire"}}}, "align_text": {"label": "Alignement du texte", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "layout": {"label": "Mise en page", "options": {"left": {"label": "Image à gauche"}, "right": {"label": "Image à droite"}}}, "divider": {"label": "Afficher le séparateur de section"}, "top_padding": {"label": "Afficher le rembourrage supérieur"}, "bottom_padding": {"label": "Afficher le rembourrage inférieur"}}, "presets": {"image_with_text": {"name": "Image avec texte"}}}, "text-columns": {"name": "Colonnes de texte avec images", "settings": {"title": {"label": "Titre"}, "align_text": {"label": "Alignement", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "divider": {"label": "Afficher le séparateur de section"}}, "blocks": {"column": {"name": "Colonne", "settings": {"enable_image": {"label": "Afficher l'image"}, "image": {"label": "Image"}, "image_width": {"label": "Largeur d'image"}, "title": {"label": "Titre"}, "text": {"label": "Texte"}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON><PERSON>"}}}}, "presets": {"text_columns_with_images": {"name": "Colonnes de texte avec images"}}}, "age-verification-popup": {"name": "Fenêtre contextuelle de vérification de l'âge", "settings": {"enable_age_verification_popup": {"label": "Afficher la fenêtre contextuelle de vérification de l'âge"}, "enable_test_mode": {"label": "Activer le mode test", "info": "Force la vérification à s'afficher à chaque actualisation et ne doit être utilisée que pour modifier la fenêtre contextuelle. Assurez-vous que le « mode test » est désactivé lors du lancement de votre boutique."}, "header_background_image": "Image de fond", "image": {"label": "Image", "info": "2000 x 800px recommandé"}, "blur_image": {"label": "Brouiller l'image"}, "header_age_verification_question": "Question de vérification de l'âge", "heading": {"label": "Titre"}, "text": {"label": "Question de vérification de l'âge"}, "decline_button_label": {"label": "Refuser le texte du bouton"}, "approve_button_label": {"label": "Approuver le texte du bouton"}, "header_declined": "Declined", "content": "Ce contenu s'affichera si l'utilisateur ne répond pas aux exigences de la vérification.", "decline_heading": {"label": "Titre"}, "decline_text": {"label": "Texte"}, "return_button_label": {"label": "Texte du bouton de retour"}}}, "countdown": {"name": "Compte à rebours", "settings": {"layout": {"label": "Mise en page de la section", "options": {"banner": {"label": "Bannière"}, "hero": {"label": "Hero"}}}, "full_width": {"label": "<PERSON><PERSON> la pleine largeur"}, "header_colors": "Couleurs", "text_color": {"label": "Couleur du texte"}, "background_color": {"label": "<PERSON><PERSON><PERSON> de fond", "info": "Utilisé lorsqu'aucune image d'arrière-plan n'est sélectionnée."}, "header_background_image": "Image de fond", "background_image": {"label": "Image de fond"}, "overlay_color": {"label": "Re<PERSON>uv<PERSON>r"}, "overlay_opacity": {"label": "Opacité de superposition"}, "mobile_image": {"label": "Image mobile"}}, "blocks": {"timer": {"name": "<PERSON><PERSON><PERSON>", "settings": {"year": {"label": "An"}, "month": {"label": "<PERSON><PERSON>", "options": {"01": {"label": "<PERSON><PERSON>"}, "02": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "03": {"label": "Mars"}, "04": {"label": "Avril"}, "05": {"label": "<PERSON>"}, "06": {"label": "Juin"}, "07": {"label": "<PERSON><PERSON><PERSON>"}, "08": {"label": "Août"}, "09": {"label": "Septembre"}, "10": {"label": "Octobre"}, "11": {"label": "Novembre"}, "12": {"label": "Décembre"}}}, "day": {"label": "Jour"}, "hour": {"label": "<PERSON><PERSON>", "options": {"00": {"label": "00:00"}, "01": {"label": "01:00"}, "02": {"label": "02:00"}, "03": {"label": "03:00"}, "04": {"label": "04:00"}, "05": {"label": "05:00"}, "06": {"label": "06:00"}, "07": {"label": "07:00"}, "08": {"label": "08:00"}, "09": {"label": "09:00"}, "10": {"label": "10:00"}, "11": {"label": "11:00"}, "12": {"label": "12:00"}, "13": {"label": "13:00"}, "14": {"label": "14:00"}, "15": {"label": "15:00"}, "16": {"label": "16:00"}, "17": {"label": "17:00"}, "18": {"label": "18:00"}, "19": {"label": "19:00"}, "20": {"label": "20:00"}, "21": {"label": "21:00"}, "22": {"label": "22:00"}, "23": {"label": "23:00"}}}, "minute": {"label": "Minute"}, "hide_timer": {"label": "Masquer la minuterie à la fin"}, "text": {"label": "Message de fin de minuterie"}}}, "content": {"name": "Contenu", "settings": {"heading": {"label": "Titre"}, "heading_size": {"label": "<PERSON><PERSON> du titre", "options": {"small": {"label": "<PERSON>"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "large": {"label": "Grand"}}}, "text": {"label": "Texte"}, "content_alignment": {"label": "Alignement du contenu", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "button": {"name": "Bouton", "settings": {"button_link": {"label": "<PERSON>n du bouton"}, "button": {"label": "Texte du bouton"}, "button_style": {"label": "Style du bouton", "options": {"secondary": {"label": "<PERSON>tour"}, "solid": {"label": "Solide"}}}}}}, "presets": {"countdown": {"name": "Compte à rebours"}}}}, "settings_schema": {"colors": {"name": "Couleurs", "settings": {"header_general": "Général", "color_body_bg": {"label": "Arrière-plan"}, "color_body_text": {"label": "Texte"}, "color_price": {"label": "Prix"}, "color_savings_text": {"label": "Sauvegarde du prix"}, "color_borders": {"label": "Lignes et bordures"}, "color_button": {"label": "Boutons"}, "color_button_text": {"label": "Texte du bouton"}, "color_sale_tag": {"label": "<PERSON><PERSON> de vente"}, "color_sale_tag_text": {"label": "Texte de la balise de vente"}, "color_cart_dot": {"label": "Point de panier"}, "color_small_image_bg": {"label": "Fond d'image"}, "color_large_image_bg": {"label": "Fond de section d'image"}, "header_header": "<PERSON>-tête", "color_header": {"label": "Arrière-plan"}, "color_header_text": {"label": "Texte"}, "color_announcement": {"label": "Barre d'annonces"}, "color_announcement_text": {"label": "Texte de la barre d'annonce"}, "header_footer": "Pied de page", "color_footer": {"label": "Arrière-plan"}, "color_footer_text": {"label": "Texte"}, "header_menu_and_cart_drawers": "Menu et panier déroulants", "color_drawer_background": {"label": "Arrière-plan"}, "color_drawer_text": {"label": "Texte"}, "color_drawer_border": {"label": "Lignes et bordures"}, "color_drawer_button": {"label": "Boutons"}, "color_drawer_button_text": {"label": "Texte du bouton"}, "color_modal_overlays": {"label": "Superpositions"}, "header_image_treatment": "Traitement d’image", "content": "Utilisé sur les diaporamas, les hero vidéo, la grille de promotion et les en-têtes de collection.", "color_image_text": {"label": "Texte"}, "color_image_overlay": {"label": "Superposition"}, "color_image_overlay_opacity": {"label": "Opacité de la superposition"}, "color_image_overlay_text_shadow": {"label": "Intensité de l'ombre du texte"}}}, "typography": {"name": "Typographie", "settings": {"header_headings": "Titres", "type_header_font_family": {"label": "Police"}, "type_header_spacing": {"label": "Espacement des lettres"}, "type_header_base_size": {"label": "<PERSON><PERSON> de <PERSON> base"}, "type_header_line_height": {"label": "<PERSON><PERSON> <PERSON> ligne"}, "type_header_capitalize": {"label": "Mettre en majuscules"}, "type_headers_align_text": {"label": "<PERSON><PERSON> les titres"}, "header_body_text": "Corps du texte", "type_base_font_family": {"label": "Police"}, "type_base_spacing": {"label": "Espacement des lettres"}, "type_base_size": {"label": "<PERSON><PERSON> de <PERSON> base"}, "type_base_line_height": {"label": "<PERSON><PERSON> <PERSON> ligne"}, "type_body_align_text": {"label": "<PERSON><PERSON> le texte"}, "header_extras": "Extras", "type_navigation_style": {"label": "Police de navigation", "options": {"body": {"label": "Corps"}, "heading": {"label": "Titre"}}}, "type_navigation_size": {"label": "Taille de la navigation"}, "type_navigation_capitalize": {"label": "Mettre la navigation en majuscules"}, "type_product_style": {"label": "Police de la grille de produits", "options": {"body": {"label": "Corps"}, "heading": {"label": "Titre"}}}, "type_product_capitalize": {"label": "Grille de produit en majuscules"}, "type_collection_font": {"label": "Police des tuiles de collection", "options": {"body": {"label": "Corps"}, "heading": {"label": "Titre"}}}, "type_collection_size": {"label": "Taille des tuiles de collection"}, "header_buttons": "Boutons", "button_style": {"label": "Style", "options": {"square": {"label": "Square"}, "round-slight": {"label": "Légèrement arrondi"}, "round": {"label": "<PERSON><PERSON>"}, "angled": {"label": "<PERSON><PERSON>"}}}, "header_icons": "Icônes", "icon_weight": {"label": "Poids", "options": {"2px": {"label": "Extra-léger"}, "3px": {"label": "<PERSON><PERSON><PERSON>"}, "4px": {"label": "<PERSON><PERSON><PERSON>"}, "5px": {"label": "Semi-gras"}, "6px": {"label": "Gras"}, "7px": {"label": "Extra-gras"}}}, "icon_linecaps": {"label": "Bords", "options": {"miter": {"label": "<PERSON><PERSON><PERSON>"}, "round": {"label": "<PERSON><PERSON>"}}}}}, "products": {"name": "Produits", "settings": {"product_save_amount": {"label": "<PERSON>ff<PERSON><PERSON> le montant épargné"}, "product_save_type": {"label": "Style d'affichage du montant épargné", "options": {"dollar": {"label": "Dollar"}, "percent": {"label": "Pourcentage"}}}, "vendor_enable": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur"}}}, "product_tiles": {"name": "<PERSON><PERSON> de produit", "settings": {"quick_shop_enable": {"label": "Activer la fonction d'achat rapide"}, "quick_shop_text": {"label": "Texte du bouton de la boutique rapide"}, "product_grid_image_size": {"label": "Forcer la taille de l'image", "options": {"natural": {"label": "Naturel"}, "square": {"label": "Carré (1:1)"}, "landscape": {"label": "Paysage (4:3)"}, "portrait": {"label": "Portrait (2:3)"}}}, "product_grid_image_fill": {"label": "Zoomer l'image pour remplir l'espace", "info": "Aucun effet lorsque la taille de l'image de la grille est réglée sur « Naturel »."}, "product_hover_image": {"label": "Passez la souris pour révéler la deuxième image"}, "header_color_swatches": "Nuanciers", "enable_swatches": {"label": "Activer les échantillons de couleur"}, "swatch_style": {"label": "Style swatch", "options": {"round": {"label": "<PERSON><PERSON>"}, "square": {"label": "Square"}}}, "header_product_reviews": "Avis produit", "content": "Ajoutez des avis en activant le paramètre ci-dessous, en installant  [l'application Shopify Product Reviews](https://apps.shopify.com/product-reviews) et en suivant notre [guide d'installation](https://archetypethemes.co/blogs/support/installing-shopifys-product-reviews-app).", "enable_product_reviews": {"label": "Activer les avis sur les produits"}}}, "collection_tiles": {"name": "<PERSON><PERSON> de collection", "settings": {"header_collection_tiles": "<PERSON><PERSON> de collection", "collection_grid_style": {"label": "Style", "options": {"overlaid": {"label": "Superposition"}, "overlaid-box": {"label": "Boîte superposée"}, "below": {"label": "Ci-dessous"}}}, "collection_grid_shape": {"label": "Forme", "options": {"square": {"label": "Carré (1:1)"}, "landscape": {"label": "Paysage (4:3)"}, "portrait": {"label": "Portrait (2:3)"}}}, "collection_grid_image": {"label": "Image", "options": {"product": {"label": "Premier produit"}, "collection": {"label": "Image de la collection"}}}, "collection_grid_text_align": {"label": "Alignement du texte", "options": {"top-left": {"label": "En haut à gauche"}, "top-center": {"label": "En haut au centre"}, "top-right": {"label": "En haut à droite"}, "left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centre"}, "right": {"label": "<PERSON><PERSON><PERSON>"}, "bottom-left": {"label": "En bas à gauche"}, "bottom-center": {"label": "En bas au centre"}, "bottom-right": {"label": "En bas à droite"}}}, "collection_grid_tint": {"label": "Teinte"}, "collection_grid_opacity": {"label": "Opacité de la teinte"}, "collection_grid_gutter": {"label": "Ajouter un espacement"}}}, "cart": {"name": "<PERSON><PERSON>", "settings": {"header_cart": "<PERSON><PERSON>", "cart_type": {"label": "Type de panier", "options": {"page": {"label": "Page"}, "drawer": {"label": "Tiroir"}}}, "cart_icon": {"label": "Icône de panier", "options": {"bag": {"label": "Sac"}, "bag-minimal": {"label": "Sac minimum"}, "cart": {"label": "<PERSON><PERSON>"}}}, "cart_additional_buttons": {"label": "Activer des boutons de paiement supplémentaires", "info": "Les boutons peuvent apparaître soit sur la page du panier, soit sur la page de paiement, mais pas les deux."}, "cart_notes_enable": {"label": "Activer les notes de commande"}, "cart_terms_conditions_enable": {"label": "Activer la case à cocher des conditions générales"}, "cart_terms_conditions_page": {"label": "Page des conditions générales"}}}, "social_media": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"header_accounts": "<PERSON><PERSON><PERSON>", "social_facebook_link": {"label": "Facebook", "info": "https://www.facebook.com/shopify"}, "social_twitter_link": {"label": "X", "info": "https://x.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://www.pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "https://instagram.com/shopify"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://www.tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_linkedin_link": {"label": "LinkedIn", "info": "https://www.linkedin.com/in/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/user/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header_sharing_options": "Options de partage", "share_facebook": {"label": "Partager sur Facebook"}, "share_twitter": {"label": "Tweeter sur Twitter"}, "share_pinterest": {"label": "<PERSON><PERSON><PERSON> sur Pinterest"}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Image favicon", "info": "Sera réduite à 32 x 32 px"}}}, "search": {"name": "Recherche", "settings": {"search_enable": {"label": "Activer la recherche"}, "search_type": {"label": "Résultats de la recherche", "options": {"product": {"label": "Produits uniquement"}, "product_page": {"label": "Produits et pages"}, "product_article": {"label": "Produits et articles"}, "product_article_page": {"label": "Produits, articles et pages"}, "product_article_page_collection": {"label": "Tout le contenu"}}}, "header_predictive_search": "Recherche prédictive", "predictive_search_enabled": {"label": "Activer la recherche prédictive", "info": "Résultats de la recherche en direct. Pas disponible dans toutes les langues. [En savoir plus](https://help.shopify.com/en/themes/development/search/predictive-search#general-requirements-and-limitations)"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le distributeur"}, "predictive_search_show_price": {"label": "<PERSON>ff<PERSON>r le prix"}, "predictive_image_size": {"label": "Rapport d'aspect de l'image du produit", "options": {"square": {"label": "Carré (1:1)"}, "landscape": {"label": "Paysage (4:3)"}, "portrait": {"label": "Portrait (2:3)"}}}}}, "extras": {"name": "Extras", "settings": {"show_breadcrumbs": {"label": "<PERSON><PERSON><PERSON><PERSON> le fil d'Ariane"}, "show_breadcrumbs_collection_link": {"label": "Afficher la page des collections dans le fil d'Ariane"}, "text_direction": {"label": "Direction du texte", "options": {"ltr": {"label": "De gauche à droite"}, "rtl": {"label": "De droite à gauche"}}}, "disable_animations": {"label": "Désactiver les animations de zoom"}}}}, "locales": {"general": {"404": {"title": "404 Page non trouvée", "subtext_html": "<p>La page que vous recherchiez n'existe pas. </p><p><a href='{{ url }}'>Continuer à acheter</a></p>>"}, "accessibility": {"skip_to_content": "Ignorer et passer au contenu", "close_modal": "Fermer (esc)", "close": "<PERSON><PERSON><PERSON>", "learn_more": "En savoir plus"}, "meta": {"tags": "Étiqueté \"{{ tags }}\"", "page": "Page {{ page }}"}, "pagination": {"previous": "Précédent", "next": "Suivant"}, "password_page": {"login_form_heading": "Entrez dans la boutique en utilisant le mot de passe", "login_form_password_label": "Mot de passe", "login_form_password_placeholder": "Votre mot de passe", "login_form_submit": "<PERSON><PERSON>", "signup_form_email_label": "E-mail", "signup_form_success": "Nous vous enverrons un e-mail juste avant l'ouverture !", "admin_link_html": "Propri<PERSON><PERSON> ma<PERSON> ? <a href=\"/admin\" class=\"text-link\">Connectez-vous ici</a>", "password_link": "Mot de passe", "powered_by_shopify_html": "Cette boutique sera propulsée par {{ shopify }}"}, "breadcrumbs": {"home": "Accueil", "home_link_title": "Retour à la page d'accueil"}, "social": {"share_on_facebook": "Partager", "share_on_x": "Partager", "share_on_pinterest": "Épinglez-le", "alt_text": {"share_on_facebook": "Partager sur Facebook", "share_on_x": "Tweeter sur X", "share_on_pinterest": "<PERSON><PERSON><PERSON> sur Pinterest"}}, "newsletter_form": {"newsletter_email": "Entrez votre e-mail", "newsletter_confirmation": "<PERSON><PERSON><PERSON> de votre inscription", "submit": "S'inscrire"}, "search": {"view_more": "Voir plus", "collections": "Collections :", "pages": "Pages :", "articles": "Articles :", "no_results_html": "Votre recherche pour \"{{ terms }}\" n'a donné aucun résultat.", "results_for_html": "Votre recherche pour \"{{ terms }}\" a donné les résultats suivants :", "title": "Recherche", "placeholder": "Rechercher dans notre boutique", "submit": "Recherche", "result_count": {"one": "{{ count }} r<PERSON><PERSON>at", "other": "{{ count }} r<PERSON><PERSON>ats"}}, "drawers": {"navigation": "Navigation sur le site", "close_menu": "<PERSON><PERSON><PERSON> le menu", "expand_submenu": "Développer le sous-menu", "collapse_submenu": "Réduire le sous-menu"}, "currency": {"dropdown_label": "Monnaie"}, "language": {"dropdown_label": "<PERSON><PERSON>"}}, "sections": {"map": {"get_directions": "Obtenir un itinéraire", "address_error": "Erreur dans la recherche de cette adresse", "address_no_results": "Aucun résultat pour cette adresse", "address_query_limit_html": "Vous avez dépassé la limite d'utilisation de l'API Google. Envisagez de passer à un <a href=\"https://developers.google.com/maps/premium/usage-limits\">Plan Premium</a>.", "auth_error_html": "Un problème est survenu lors de l'authentification de votre compte Google Maps. Créez et activez les autorisations <a href=\"https://developers.google.com/maps/documentation/javascript/get-api-key\">JavaScript API</a> et <a href=\"https://developers.google.com/maps/documentation/geocoding/get-api-key\">Geocoding API</a> de votre application."}, "slideshow": {"play_slideshow": "Lire le diaporama", "pause_slideshow": "Interrompre le diaporama"}}, "blogs": {"article": {"view_all": "<PERSON><PERSON> afficher", "tags": "Étiquettes", "read_more": "<PERSON><PERSON><PERSON> la lecture", "back_to_blog": "Retour à {{ title }}"}, "comments": {"title": "Laisser un commentaire", "name": "Nom", "email": "E-mail", "message": "Message", "post": "Publier le commentaire", "moderated": "Veuillez noter que les commentaires doivent être approuvés avant d'être publiés", "success_moderated": "Votre commentaire a bien été publié. Nous le publierons sous peu, conformément au processus de modération de notre blog.", "success": "Votre commentaire a bien été publié ! Merci !", "with_count": {"one": "{{ count }} commentaire", "other": "{{ count }} commentaires"}}}, "cart": {"general": {"title": "<PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "note": "Note de commande", "subtotal": "Sous-total", "discounts": "Remises", "shipping_at_checkout": "Expédition, taxes et codes de réduction calculés à la caisse.", "update": "Mettre à jour le panier", "checkout": "Procéder au paiement", "empty": "Votre panier est actuellement vide.", "continue_browsing_html": "<a href='{{ url }}'>Continuer à acheter</a>", "close_cart": "<PERSON><PERSON><PERSON> le panier", "reduce_quantity": "Réduire la quantité d'articles d'une unité", "increase_quantity": "Augmenter la quantité d'articles d'une unité", "terms": "J'accepte les conditions générales", "terms_html": "J'accepte les <a href='{{ url }}' target='_blank'>conditions générales</a>", "terms_confirm": "<PERSON><PERSON> de<PERSON> accepter les conditions générales de vente pour passer à la caisse."}, "label": {"price": "Prix", "quantity": "Quantité", "total": "Total"}}, "collections": {"general": {"catalog_title": "Catalogue", "all_of_collection": "<PERSON><PERSON> afficher", "view_all_products_html": "Voir tous les<br>{{ count }} produits", "see_more": "Afficher plus", "see_less": "Affiche<PERSON> moins", "no_matches": "Désolé, il n'y a aucun produit dans cette collection.", "items_with_count": {"one": "{{ count }} produit", "other": "{{ count }} produits"}}, "sorting": {"title": "<PERSON><PERSON>"}, "filters": {"title_tags": "<PERSON><PERSON><PERSON>", "all_tags": "Tous les produits", "categories_title": "Catégories"}}, "contact": {"form": {"name": "Nom", "email": "E-mail", "phone": "Numéro de téléphone", "message": "Message", "send": "Envoyer", "post_success": "Merci de nous avoir contactés. Nous vous répondrons dès que possible."}}, "customer": {"account": {"title": "Mon compte", "details": "<PERSON>é<PERSON> du compte", "view_addresses": "Voir les adresses", "return": "Retourner au compte"}, "activate_account": {"title": "<PERSON><PERSON> le compte", "subtext": "<PERSON><PERSON><PERSON> votre mot de passe pour activer le compte.", "password": "Mot de passe", "password_confirm": "Confirmer le mot de passe", "submit": "<PERSON><PERSON> le compte", "cancel": "Décliner l'invitation"}, "addresses": {"title": "Adresses", "default": "<PERSON><PERSON> <PERSON><PERSON>", "add_new": "Ajouter l'adresse", "edit_address": "Modifier l'adresse", "first_name": "Prénom", "last_name": "Nom de famille", "company": "Entreprise", "address1": "Adresse1", "address2": "Adresse2", "city": "Ville", "country": "Pays", "province": "Province", "zip": "Code postal/Zip", "phone": "Téléphone", "set_default": "Définir comme adresse par défaut", "add": "Ajouter l'adresse", "update": "Mettre à jour l'adresse", "cancel": "Annuler", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete_confirm": "Êtes-vous certain(e) de vouloir supprimer cette adresse ?"}, "login": {"title": "Connexion", "email": "E-mail", "password": "Mot de passe", "forgot_password": "Mot de passe oublié ?", "sign_in": "Se connecter", "cancel": "Retourner à la boutique", "guest_title": "Continuer en tant qu'invité(e)", "guest_continue": "<PERSON><PERSON><PERSON>"}, "orders": {"title": "Historique de la commande", "order_number": "Commande", "date": "Date", "payment_status": "Statut des paiements", "fulfillment_status": "Statut des commandes", "total": "Total", "none": "Vous n'avez encore passé aucune commande."}, "order": {"title": "Commande {{ name }}", "date_html": "<PERSON><PERSON> le {{ date }}", "cancelled_html": "Commande annulée le {{ date }}", "cancelled_reason": "Motif : {{ reason }}", "billing_address": "Adresse de facturation", "payment_status": "Statut des paiements", "shipping_address": "Adresse d'expédition", "fulfillment_status": "Statut des commandes", "discount": "Réduction", "shipping": "Expédition", "tax": "Taxes", "product": "Produit", "sku": "SKU", "price": "Prix", "quantity": "Quantité", "total": "Total", "fulfilled_at_html": "Trai<PERSON><PERSON> le {{ date }}", "subtotal": "Sous-total"}, "recover_password": {"title": "Réinitialiser son mot de passe", "email": "E-mail", "submit": "Envoyer", "cancel": "Annuler", "subtext": "Nous vous enverrons un e-mail pour réinitialiser votre mot de passe.", "success": "Nous vous avons envoyé un e-mail contenant un lien pour mettre à jour votre mot de passe."}, "reset_password": {"title": "Réinitialiser le mot de passe du compte", "subtext": "Saisir un nouveau mot de passe pour {{ email }}", "password": "Mot de passe", "password_confirm": "Confirmer le mot de passe", "submit": "Réinitialiser le mot de passe"}, "register": {"title": "<PERSON><PERSON><PERSON> un compte", "first_name": "Prénom", "last_name": "Nom de famille", "email": "E-mail", "password": "Mot de passe", "submit": "<PERSON><PERSON><PERSON>", "cancel": "Retourner à la boutique"}}, "home_page": {"onboarding": {"product_title": "Exemple de produit", "product_description": "Cette zone est utilisée pour décrire les détails de votre produit. Parlez aux clients de l'aspect, de la nature et du style de votre produit. Ajoutez des détails sur la couleur, les matériaux utilisés, la taille et l'endroit où il a été fabriqué.", "collection_title": "Collection d'exemples", "no_content": "Cette section ne contient actuellement aucun contenu. Ajoutez du contenu à cette section en utilisant la barre latérale."}}, "layout": {"cart": {"title": "<PERSON><PERSON>"}, "customer": {"account": "<PERSON><PERSON><PERSON>", "log_out": "Déconnexion", "log_in": "Connexion", "create_account": "<PERSON><PERSON><PERSON> un compte"}, "footer": {"social_platform": "{{ name }} sur {{ platform }}"}}, "products": {"general": {"color_swatch_trigger": "<PERSON><PERSON><PERSON>", "size_trigger": "<PERSON><PERSON>", "size_chart": "Tableau des tailles", "save_html": "Sauvegarder {{ saved_amount }}", "collection_return": "Retour à {{ collection }}", "next_product": "Suivant : {{ title }}", "sale": "Vente", "sale_price": "Prix soldé", "regular_price": "Prix habituel", "from_text_html": "à partir de {{ price }}", "recent_products": "Produits récemment consultés", "reviews": "Critiques"}, "product": {"description": "Description", "in_stock_label": "En stock, prêt à être expédié", "stock_label": {"one": "Rupture de stock : {{ count }} article restant", "other": "Rupture de stock : {{ count }} articles restants"}, "sold_out": "Vendu", "unavailable": "Non disponible", "quantity": "Quantité", "add_to_cart": "A<PERSON>ter au panier", "preorder": "Pré-commande", "include_taxes": "Taxes incluses.", "shipping_policy_html": "<a href='{{ link }}'>Envoi</a> calculé à la caisse.", "will_not_ship_until": "<PERSON>r<PERSON><PERSON> à être expédié {{ date }}", "will_be_in_stock_after": "De retour en stock {{ date }}", "waiting_for_stock": "Inventaire en cours de livraison", "view_in_space": "Afficher dans son espace", "view_in_space_label": "Affichage dans votre espace, charge l'article dans une fenêtre de réalité augmentée"}}, "store_availability": {"general": {"view_store_info": "Afficher les informations de la boutique", "check_other_stores": "Vérifier la disponibilité dans d'autres boutiques", "pick_up_available": "Service de retrait disponible", "pick_up_currently_unavailable": "Enlèvement actuellement indisponible", "pick_up_available_at_html": "Enlèvement disponible à <strong>{{ location_name }}</strong>", "pick_up_unavailable_at_html": "Retrait actuellement non disponible à <strong>{{ localisation_name }}</strong>"}}, "gift_cards": {"issued": {"title_html": "Voici votre carte-cadeau {{ shop }} d'une valeur de {{ value }} !", "subtext": "Voici votre carte cadeau !", "disabled": "Désactivé", "expired": "Expiré le {{ expiry }}", "active": "Expire le {{ expiry }}", "redeem": "Utilisez ce code à la caisse pour échanger votre carte cadeau", "shop_link": "Début des achats", "print": "<PERSON><PERSON><PERSON><PERSON>", "add_to_apple_wallet": "Ajouter à Apple Wallet"}}, "date_formats": {"month_day_year": "%b %d, %Y"}}, "product_block": {"price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "size_chart": {"name": "Guide des tailles", "settings": {"page": {"label": "Dimenssionnez la page des graphiques"}}}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"variant_labels": {"label": "Afficher les étiquettes des variantes"}, "picker_type": {"label": "Type", "options": {"button": {"label": "Boutons"}, "dropdown": {"label": "<PERSON><PERSON>"}}}, "color_swatches": {"label": "Activer les échantillons de couleur", "info": "Le type doit être défini sur « Boutons ». [Apprenez à mettre en place des échantillons](https://archetypethemes.co/blogs/impulse/how-do-i-set-up-color-swatches)"}, "product_dynamic_variants_enable": {"label": "Activer les options de produit dynamiques"}}}, "description": {"name": "Description", "settings": {"is_tab": {"label": "Afficher sous forme d'onglet"}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Affichez le bouton de paiement dynamique", "info": "Permet aux clients de régler leurs achats directement en utilisant un mode de paiement familier. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "surface_pickup_enable": {"label": "Activer la fonction de disponibilité de la collecte", "info": "Apprenez à installer cette fonction [ici](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)"}}}, "inventory_status": {"name": "État du stock", "settings": {"inventory_threshold": {"label": "Seuil de stocks bas"}, "inventory_transfers_enable": {"label": "Affichez l'avis de transfert de stock", "info": "Apprenez à créer des transferts de stock [ici](https://help.shopify.com/en/manual/products/inventory/transfers/create-transfer)"}}}, "sales_point": {"name": "Point de vente", "settings": {"icon": {"label": "Icône", "options": {"checkmark": {"label": "Coche"}, "gift": {"label": "<PERSON><PERSON>"}, "globe": {"label": "Globe"}, "heart": {"label": "<PERSON><PERSON><PERSON>"}, "leaf": {"label": "<PERSON><PERSON><PERSON>"}, "lock": {"label": "Cadenas"}, "package": {"label": "Emballage"}, "phone": {"label": "Téléphone"}, "ribbon": {"label": "Ruban"}, "shield": {"label": "Bouclier"}, "tag": {"label": "Étiquette"}, "truck": {"label": "Camion"}}}, "text": {"label": "Texte"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte"}}}, "trust_badge": {"name": "Badge de confiance", "settings": {"trust_image": {"label": "Image"}}}, "tab": {"name": "Onglet", "settings": {"title": {"label": "Titre"}, "content": {"label": "Contenu de l'onglet"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> le contenu de la page"}}}, "share_on_social": {"name": "Partager sur les réseaux sociaux", "settings": {"content": "Choisissez les plates-formes à partager dans les paramètres globaux du thème."}}, "separator": {"name": "Séparateur"}, "contact_form": {"name": "Formulaire de contact", "settings": {"content": "Toutes les soumissions sont envoyées à l'adresse e-mail du client de votre magasin. [En savoir plus](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).", "title": {"label": "Titre"}, "phone": {"label": "Ajouter un champ de numéro de téléphone"}}}, "html": {"name": "HTML", "settings": {"code": {"label": "HTML", "info": "Prend en charge Liquid"}}}}, "common": {"color_scheme": {"label": "Combinaison de couleurs", "options": {"none": {"label": "Aucun"}, "custom_1": {"label": "Personnalisé 1"}, "custom_2": {"label": "Personnalisé 2"}, "custom_3": {"label": "Personnalisé 3"}}}, "enable_swatch_labels": {"label": "Afficher les étiquettes des échantillons"}, "lazyload_images": {"label": "Images de chargement paresseux", "info": "Le chargement différé doit être activé lorsque les images de section sont sous le pli. [En savoir plus](https://archetypethemes.co/blogs/support/what-is-lazyloading)"}, "subheading": {"label": "Sous-titre"}, "heading": {"label": "Titre"}, "richtext": {"label": "Texte"}, "text_highlight": {"label": "Titre style de texte en italique", "info": "Les styles s'appliquent uniquement au texte en italique dans le titre", "options": {"underline": {"label": "<PERSON><PERSON><PERSON>"}, "outline": {"label": "<PERSON>tour"}, "serif": {"label": "<PERSON><PERSON>"}, "handwrite": {"label": "Écriture"}, "accent-color": {"label": "Paramètre de couleur des 'étiquettes de vente'"}, "regular": {"label": "Ré<PERSON>lier"}}}, "button_text": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "heading_size": {"label": "<PERSON><PERSON> du titre", "options": {"extra_large": {"label": "Extra-grande"}, "large": {"label": "Grande"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "small": {"label": "Petite"}}}, "text_position": {"label": "Position du texte", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centre"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "heading_position": {"label": "Position du titre", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centre"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "content_alignment": {"label": "Alignement du contenu", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}, "right": {"label": "<PERSON><PERSON><PERSON>"}}}, "text_alignment": {"label": "Alignement du texte"}, "content_position": {"label": "Emplacement du contenu", "options": {"top": {"label": "<PERSON><PERSON>"}, "center": {"label": "Centre"}, "bottom": {"label": "Bas"}}}, "top_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> le remplissage supérieur"}, "bottom_padding": {"label": "Aff<PERSON>r le remplissage inférieur"}, "full_width": {"label": "<PERSON><PERSON> la pleine largeur"}, "layout": {"space_above": {"label": "Ajouter un espacement supérieur"}, "space_below": {"label": "Ajouter l'espacement inférieur"}, "gutter_size": {"label": "Espacement de la grille"}}, "image_size": {"label": "Largeur d'image", "options": {"extra_large": {"label": "Extra-grande"}, "large": {"label": "Grande"}, "medium": {"label": "<PERSON><PERSON><PERSON>"}, "small": {"label": "Petite"}}}, "image_crop": {"label": "Recadrage de l'image"}, "image_mask": {"label": "Forme de l'image", "options": {"none": {"label": "Aucun"}, "portrait": {"label": "Portrait"}, "landscape": {"label": "Paysage"}, "square": {"label": "Carré"}, "rounded": {"label": "Arrondi"}, "rounded-wave": {"label": "<PERSON><PERSON> arrondie"}, "rounded-top": {"label": "Cambre"}, "star": {"label": "<PERSON><PERSON><PERSON>"}, "splat-1": {"label": "Éclaboussure 1"}, "splat-2": {"label": "Éclaboussure 2"}, "splat-3": {"label": "Éclaboussure 3"}, "splat-4": {"label": "Éclaboussure 4"}}}, "products": {"max_products": {"label": "Quantité maximale de produits à afficher"}}, "images": {"hide_on_mobile": {"label": "Masquer tous les blocs d'images sur le portable"}}, "gift_card": {"show_gift_card_recipient": {"label": "Afficher le formulaire d’information sur le destinataire pour les cartes‑cadeaux en tant que produit", "info": "Les cartes-cadeaux en tant que produits peuvent être envoyées directement au destinataire avec un message personnel. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}, "follow_on_shop": {"name": "Suivre sur Shop", "paragraph": {"content": "Pour autoriser les clients à suivre votre boutique sur l’application Shop depuis votre boutique en ligne, Shop Pay doit être activé. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "button": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}}, "text_with_icons": {"name": {"label": "Texte avec icônes"}, "settings": {"title": {"label": "Titre"}, "align_text": {"label": "Alignement du texte", "options": {"left": {"label": "G<PERSON><PERSON>"}, "center": {"label": "Centré"}}}, "desktop_columns_per_row": {"label": "Colonnes par ligne (ordinateur)"}, "icon_color": {"label": "Couleur de l'icône"}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "divider": {"label": "Séparateur"}, "alt": {"label": "Utiliser une autre couleur de section"}}, "blocks": {"column": {"name": "Colonne", "settings": {"icon": {"label": "Icône", "options": {"bills": {"label": "Factures"}, "calendar": {"label": "<PERSON><PERSON><PERSON>"}, "cart": {"label": "<PERSON><PERSON>"}, "charity": {"label": "<PERSON><PERSON><PERSON>"}, "chat": {"label": "Cha<PERSON>"}, "envelope": {"label": "Enveloppe"}, "gears": {"label": "Engrenages"}, "gift": {"label": "<PERSON><PERSON>"}, "globe": {"label": "Globe"}, "package": {"label": "<PERSON><PERSON>"}, "phone": {"label": "Téléphone"}, "plant": {"label": "Plante"}, "recycle": {"label": "Recyclage"}, "ribbon": {"label": "Ruban"}, "sales-tag": {"label": "Étiquette de vente"}, "shield": {"label": "Bouclier"}, "stopwatch": {"label": "Chronomètre"}, "store": {"label": "Ma<PERSON><PERSON>"}, "thumbs-up": {"label": "<PERSON><PERSON> vers le haut"}, "trophy": {"label": "Trophée"}, "truck": {"label": "Camion"}, "wallet": {"label": "Portefeuille"}}}, "title": {"label": "Titre"}, "text": {"label": "Texte"}}}}, "presets": {"text_with_icons": {"name": "Texte avec icônes"}}}, "advanced-accordion": {"name": "Accord<PERSON><PERSON> a<PERSON>", "settings": {"disabled": {"label": "Désactiver l'accordéon"}, "per_row": {"label": "Blocs par rangée"}, "two_per_row_mobile": {"label": "Deux blocs par rangée sur mobile"}, "opened": {"label": "S'affiche initialement comme ouvert"}}, "blocks": {"text_block": {"name": "Bloc de texte", "settings": {"enable_image": {"label": "Afficher l'image"}, "image": {"label": "Image"}, "image_width": {"label": "Largeur de l'image"}}}, "link_block": {"name": "Bloc de liens", "settings": {"link_label": {"label": "Libellé du lien"}, "link": {"label": "<PERSON><PERSON>"}, "show_arrow": {"label": "Afficher la flèche"}}}, "html_block": {"name": "Bloc HTML", "settings": {"html": {"label": "HTML"}}}}}, "media_with_text": {"name": "Média avec texte", "header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "media_width": {"label": "Largeur du support"}, "media_crop": {"label": "Recadrage média"}, "layout": {"label": "Disposition sur le bureau", "options": {"left": {"label": "Médias à gauche"}, "right": {"label": "Médias à droite"}}}, "content_header": {"content": "Contenu"}, "blocks": {"video": {"label": "Vidéo", "autoplay": {"label": "Lecture automatique de la vidéo"}, "loop": {"label": "<PERSON><PERSON><PERSON> vid<PERSON>o"}, "hide_controls": {"label": "Masquer les contrôles"}, "mute_video": {"label": "Couper la vidéo"}, "alt_image_content": {"content": "Image de remplacement"}, "alt_image": {"label": "Image", "info": "L'image s'affichera si la vidéo ne peut pas se charger"}}, "image": {"label": "Image"}}}, "gallery": {"name": "Galerie", "header": {"content": "Contenu", "layout": "Mise en page"}, "images_per_row": {"label": "Images par ligne"}, "image_alignment": {"label": "Alignement des images"}, "image": {"label": "Image"}}}}