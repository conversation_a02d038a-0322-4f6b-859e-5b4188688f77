{%- liquid
  assign current_filter_size = 0

  for filter in collection.filters
    assign current_filter_size = current_filter_size | plus: filter.active_values.size
  endfor
-%}

<div
  class="collection-content"
  data-section-id="{{ section.id }}"
  data-section-type="collection-grid"
>
  <div id="CollectionAjaxContent">
    <div class="page-width">
      <div class="grid">
        <div class="grid__item medium-up--one-fifth grid__item--sidebar">
          {%- render 'collection-grid-filters',
            collection: collection,
            enable_sidebar: section.settings.enable_sidebar,
            filter_style: section.settings.filter_style,
            collapsed: section.settings.collapsed,
            enable_color_swatches: section.settings.enable_color_swatches,
            enable_swatch_labels: section.settings.enable_swatch_labels
          -%}
        </div>
        <div class="grid__item medium-up--four-fifths grid__item--content">
          {%- liquid
            for block in section.blocks
              case block.type
              when 'product_grid'
                assign paginate_by = block.settings.per_row | times: block.settings.rows_per_page
              endcase
            endfor
          -%}
          {%- paginate collection.products by paginate_by -%}
          {%- for block in section.blocks -%}
            <div {{ block.shopify_attributes }} {%- if block.type == 'product-grid' -%}data-scroll-to{%- endif -%}>
              {%- case block.type -%}
              {%- when 'collection_description' -%}
                {%- if collection.description != blank and current_filter_size == 0 -%}
                  <div class="rte collection__description">
                    {{ collection.description }}
                  </div>
                {%- endif -%}
              {%- when 'subcollections' -%}
                {%- if paginate.current_page == 1 and current_filter_size == 0 -%}
                  {%- render 'subcollections', per_row: block.settings.subcollections_per_row -%}
                {%- endif -%}
              {%- when 'product_grid' -%}
                <div data-scroll-to>
                  {% render 'collection-grid',
                    collection: collection,
                    items: collection.products,
                    enable_sidebar: section.settings.enable_sidebar,
                    filter_style: section.settings.filter_style,
                    enable_sort: section.settings.enable_sort,
                    enable_collection_count: block.settings.enable_collection_count,
                    per_row: block.settings.per_row,
                    mobile_flush_grid: block.settings.mobile_flush_grid,
                    quick_shop_enable: settings.quick_shop_enable
                  %}
                  {%- if paginate.pages > 1 -%}
                    {%- render 'pagination', paginate: paginate -%}
                  {%- endif -%}
                </div>
              {%- endcase -%}
            </div>
          {%- endfor -%}

          {%- endpaginate -%}
        </div>
      </div>
    </div>
  </div>
</div>

{%- if section.settings.enable_sidebar == false or section.settings.filter_style == 'drawer' or collection.filters.size == 0 -%}
{% comment %}
  Override grid styles if sidebar is disabled
{% endcomment %}
{% style %}
  .collection-content .grid__item--sidebar { width: 0; padding:0; }
  .collection-content .grid__item--content { width: 100%; }
  .grid__item--sidebar { position: static; overflow: hidden; }
{% endstyle %}
{%- endif -%}

<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "CollectionPage",
  {% if collection.description != blank %}
    "description": {{ collection.description | strip_html | json }},
  {% endif %}
  {% if page_image %}
    {% assign image_size = page_image.width | append: 'x' %}
    "image": {
      "@type": "ImageObject",
      "height": {{ page_image.height | json }},
      "url": {{ page_image | img_url: image_size | prepend: "https:" | json }},
      "width": {{ page_image.width | json }}
    },
  {% endif %}
  "name": {{ collection.title | json }}
}
</script>

{% schema %}
{
  "name": "t:sections.main-collection.name",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.main-collection.settings.header_filtering_and_sorting"
    },
    {
      "type": "checkbox",
      "id": "enable_sidebar",
      "label": "t:sections.main-collection.settings.enable_sidebar.label",
      "default": true,
      "info": "t:sections.main-collection.settings.enable_sidebar.info"
    },
    {
      "type": "checkbox",
      "id": "collapsed",
      "label": "t:sections.main-collection.settings.collapsed.label",
      "default": true
    },
    {
      "type": "select",
      "id": "filter_style",
      "label": "t:sections.main-collection.settings.filter_style.label",
      "default": "sidebar",
      "options": [
        {
          "value": "sidebar",
          "label": "t:sections.main-collection.settings.filter_style.options.sidebar.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-collection.settings.filter_style.options.drawer.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "enable_color_swatches",
      "label": "t:sections.main-collection.settings.enable_color_swatches.label",
      "info": "t:sections.main-collection.settings.enable_color_swatches.info"
    },
    {
      "type": "checkbox",
      "id": "enable_swatch_labels",
      "label": "t:common.enable_swatch_labels.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_sort",
      "label": "t:sections.main-collection.settings.enable_sort.label",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "collection_description",
      "name": "t:sections.main-collection.blocks.collection_description.name",
      "limit": 1
    },
    {
      "type": "product_grid",
      "name": "t:sections.main-collection.blocks.products.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "enable_collection_count",
          "label": "t:sections.main-collection.blocks.products.settings.enable_collection_count.label",
          "default": true
        },
        {
          "type": "range",
          "id": "per_row",
          "label": "t:sections.main-collection.blocks.products.settings.per_row.label",
          "default": 4,
          "min": 2,
          "max": 5,
          "step": 1
        },
        {
          "type": "range",
          "id": "rows_per_page",
          "label": "t:sections.main-collection.blocks.products.settings.rows_per_page.label",
          "default": 7,
          "min": 3,
          "max": 20,
          "step": 1
        },
        {
          "type": "checkbox",
          "id": "mobile_flush_grid",
          "label": "t:sections.main-collection.blocks.products.settings.mobile_flush_grid.label",
          "default": false
        }
      ],
      "limit": 1
    },
    {
      "type": "subcollections",
      "name": "t:sections.main-collection.blocks.subcollections.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-collection.blocks.subcollections.settings.content"
        },
        {
          "type": "range",
          "id": "subcollections_per_row",
          "label": "t:sections.main-collection.blocks.subcollections.settings.subcollections_per_row.label",
          "default": 5,
          "min": 2,
          "max": 5,
          "step": 1
        }
      ],
      "limit": 1
    }
  ]
}
{% endschema %}
