{%- assign per_row = section.settings.per_row -%}
{%- assign product_limit = per_row | times: section.settings.rows -%}
{%- assign collection = collections[section.settings.home_featured_products] -%}

{%- if section.settings.divider -%}<div class="section--divider">{%- endif -%}

<div
  id="CollectionSection-{{ section.id }}"
  data-section-id="{{ section.id }}"
  data-section-type="collection-grid"
  data-context="featured-collection">
  {%- if section.settings.title != blank or section.settings.view_all -%}
    <div class="page-width">
      <div class="section-header{% if section.settings.view_all %}{% unless settings.type_headers_align_text %} section-header--with-link{% endunless %}{% endif %}">
        <h2 class="section-header__title">
          {{ section.settings.title }}
        </h2>
        {%- if section.settings.view_all and section.settings.rows == 1 -%}
          <a href="{{ collections[section.settings.home_featured_products].url }}" class="btn btn--secondary btn--small section-header__link">{{ 'collections.general.all_of_collection' | t }}</a>
        {%- endif -%}
      </div>
    </div>
  {%- endif -%}

  <div class="page-width{% if section.settings.mobile_scrollable %} page-width--flush-small{% endif %}">
    <div{% if section.settings.mobile_scrollable %} class="grid-overflow-wrapper"{% endif %}>
      <div class="grid grid--uniform"{% if section.settings.mobile_scrollable %} data-aos="overflow__animation"{% endif %}>
        {%- liquid
          capture gridView
            render 'products_per_row', products_per_row: per_row, style: 'fractions'
          endcapture

          if section.settings.mobile_scrollable
            assign fallback = '39vw'
          else
            assign fallback = 'variable'
          endif
        -%}

        {%- if section.settings.home_featured_products == blank or collections[section.settings.home_featured_products].empty? or collections[section.settings.home_featured_products].products_count == 0 -%}

          {%- unless emptyState -%}
            {%- assign emptyState = true -%}
          {%- endunless -%}

          <div class="grid__item">
            <div class="grid grid--uniform">
              {%- for i in (1..product_limit) -%}
                <div class="grid__item grid-product {{ gridView }}" data-aos="row-of-{{ per_row }}">
                  <div class="grid-product__content">
                    <a href="{{ product.url | within: collection }}" class="grid-product__link">
                      <div class="grid-product__image-mask">
                        {%- capture current -%}{% cycle 1, 2, 3, 4, 5, 6 %}{%- endcapture -%}
                        <div class="image-wrap">{{ 'product-' | append: current | placeholder_svg_tag: 'placeholder-svg' }}</div>
                      </div>
                      <div class="grid-product__meta">
                        <div class="grid-product__title">{{ 'home_page.onboarding.product_title' | t }}</div>
                        <div class="grid-product__price">$29</div>
                      </div>
                    </a>
                  </div>
                </div>
              {%- endfor -%}
            </div>
          </div>

        {%- else -%}

          {%- for product in collections[section.settings.home_featured_products].products limit: product_limit -%}
            {%- render 'product-grid-item',
              product: product,
              collection: collection,
              per_row: per_row,
              quick_shop_enable: settings.quick_shop_enable,
              fallback: fallback,
            -%}
          {%- endfor -%}

          {%- if section.settings.view_all -%}

            {%- if section.settings.rows > 1 -%}
              <div class="grid__item text-center{% if section.settings.mobile_scrollable %} small--hide{% endif %}">
                <a href="{{ collections[section.settings.home_featured_products].url }}" class="btn">{{ 'collections.general.all_of_collection' | t }}</a>
              </div>
            {%- endif -%}

            {%- if section.settings.mobile_scrollable -%}
              <div class="grid__item grid__item--view-all text-center {{ gridView }} medium-up--hide">
                <a href="{{ collections[section.settings.home_featured_products].url }}" class="grid-product__see-all">
                  {{ 'collections.general.view_all_products_html' | t: count: collection.products_count }}
                </a>
              </div>
            {%- endif -%}

          {%- endif -%}

        {%- endif -%}
      </div>
    </div>
  </div>
</div>

{%- if section.settings.divider -%}</div>{%- endif -%}

{% schema %}
{
  "name": "t:sections.featured-collection.name",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.featured-collection.settings.title.label",
      "default": "Featured collection"
    },
    {
      "type": "collection",
      "id": "home_featured_products",
      "label": "t:sections.featured-collection.settings.home_featured_products.label"
    },
    {
      "type": "range",
      "id": "per_row",
      "label": "t:sections.featured-collection.settings.per_row.label",
      "default": 4,
      "min": 1,
      "max": 5,
      "step": 1
    },
    {
      "type": "range",
      "id": "rows",
      "label": "t:sections.featured-collection.settings.rows.label",
      "default": 1,
      "min": 1,
      "max": 5,
      "step": 1
    },
    {
      "type": "checkbox",
      "id": "mobile_scrollable",
      "label": "t:sections.featured-collection.settings.mobile_scrollable.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "view_all",
      "label": "t:sections.featured-collection.settings.view_all.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.featured-collection.settings.divider.label",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-collection.presets.featured_collection.name"
    }
  ],
  "blocks": [],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
