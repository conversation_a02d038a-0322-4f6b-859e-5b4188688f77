<div class="index-section">
  <div class="page-width{% if section.settings.narrow_column %} page-width--narrow{% endif %}">
    {%- if section.settings.title != blank -%}
      <div class="section-header">
        <h2 class="section-header__title">
          {{ section.settings.title }}
        </h2>
        {%- if section.settings.text != blank -%}
          <div class="rte section-header__rte">{{ section.settings.text }}</div>
        {%- endif -%}
      </div>
    {% endif %}

    <div class="form-vertical">
      {%- assign form_id = 'contact-' | append: section.id -%}
      {%- form 'contact', id: form_id -%}

        {%- if form.posted_successfully? -%}
          <p class="note note--success">
            {{ 'contact.form.post_success' | t }}
          </p>
        {%- endif -%}

        {{ form.errors | default_errors }}

        <div class="grid grid--small">
          <div class="grid__item medium-up--one-half">
            <label for="ContactFormName-{{ section.id }}">{{ 'contact.form.name' | t }}</label>
            <input type="text" id="ContactFormName-{{ section.id }}" class="input-full" name="contact[name]" autocapitalize="words" value="{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}">
          </div>

          <div class="grid__item medium-up--one-half">
            <label for="ContactFormEmail-{{ section.id }}">{{ 'contact.form.email' | t }}</label>
            <input type="email" id="ContactFormEmail-{{ section.id }}" class="input-full" name="contact[email]" autocorrect="off" autocapitalize="off" value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}">
          </div>
        </div>

        {%- if section.settings.show_phone -%}
          <label for="ContactFormPhone-{{ section.id }}">{{ 'contact.form.phone' | t }}</label>
          <input type="tel" id="ContactFormPhone-{{ section.id }}" class="input-full" name="contact[phone]" pattern="[0-9\-]*" value="{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}">
        {%- endif -%}

        <label for="ContactFormMessage-{{ section.id }}">{{ 'contact.form.message' | t }}</label>
        <textarea rows="5" id="ContactFormMessage-{{ section.id }}" class="input-full" name="contact[body]">{% if form.body %}{{ form.body }}{% endif %}</textarea>

        <label for="contact-form-submit-{{ section.id }}" class="hidden-label">{{ 'contact.form.send' | t }}</label>
        <button type="submit" id="contact-form-submit-{{ section.id }}" class="btn">
          {{ 'contact.form.send' | t }}
        </button>

        {% comment %}
          Remove the following three lines of code to remove the note
          about being protected by Google's reCAPTCHA service.
          By removing it, the small reCAPTCHA widget will appear in the
          bottom right corner of the page.
        {% endcomment %}
        {{ 'shopify.online_store.spam_detection.disclaimer_html' | t }}

      {%- endform -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.contact-form.name",
  "class": "index-section",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.contact-form.settings.content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.contact-form.settings.title.label",
      "default": "Contact us"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "t:sections.contact-form.settings.text.label"
    },
    {
      "type": "checkbox",
      "id": "show_phone",
      "label": "t:sections.contact-form.settings.show_phone.label"
    },
    {
      "type": "checkbox",
      "id": "narrow_column",
      "label": "t:sections.contact-form.settings.narrow_column.label",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "t:sections.contact-form.presets.contact_form.name"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
