{%- liquid
  assign recommend_products = true

  if recommendations.products and recommendations.products_count > 0
    assign related_collection = recommendations
  endif

  assign number_of_products = section.settings.related_count
-%}

<product-recommendations
  id="Recommendations-{{ section.id }}"
  data-section-id="{{ section.id }}"
  data-section-type="product-recommendations"
  data-enable="{{ recommend_products }}"
  data-product-id="{{ product.id }}"
  data-intent="related"
  data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ number_of_products }}"
  data-limit="{{ number_of_products }}">

  <div
    data-section-id="{{ product.id }}"
    data-subsection
    data-section-type="collection-grid"
    class="index-section">
    <div class="page-width">
      <header class="section-header">
        <h3 class="section-header__title">
          {{ section.settings.product_recommendations_heading }}
        </h3>
      </header>
    </div>

    <div class="page-width page-width--flush-small">
      <div class="grid-overflow-wrapper">
        {%- if recommend_products -%}
          <div class="product-recommendations-placeholder">
            {% comment %}
              This content is visually hidden and replaced when recommended products show up
            {% endcomment %}
            <div class="grid grid--uniform visually-invisible" aria-hidden="true">
              {%- render 'product-grid-item',
                product: product,
                quick_shop_enable: settings.quick_shop_enable
              -%}
            </div>
          </div>
        {%- endif -%}
        {%- if related_collection.products_count > 0 -%}
          <div class="product-recommendations page-width">
            <div class="grid grid--uniform" data-aos="overflow__animation">
              {%- for product in related_collection.products limit: number_of_products -%}
                {% comment %} On smaller screen sizes, 39vw is used for grid items in the CSS {% endcomment %}
                {%- render 'product-grid-item',
                  product: product,
                  per_row: section.settings.products_per_row,
                  quick_shop_enable: settings.quick_shop_enable,
                  fallback: '39vw',
                -%}
              {%- endfor -%}
            </div>
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
</product-recommendations>

{% schema %}
{
  "name": "t:sections.product-recommendations.name",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.product-recommendations.settings.show_product_recommendations.info"
    },
    {
      "type": "text",
      "id": "product_recommendations_heading",
      "label": "t:sections.product-recommendations.settings.product_recommendations_heading.label",
      "default": "You may also like"
    },
    {
      "type": "range",
      "id": "related_count",
      "label": "t:sections.product-recommendations.settings.related_count.label",
      "default": 6,
      "min": 2,
      "max": 6,
      "step": 1
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "t:sections.product-recommendations.settings.products_per_row.label",
      "default": 3,
      "min": 2,
      "max": 5,
      "step": 1
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
