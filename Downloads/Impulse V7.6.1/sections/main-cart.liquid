<div class="page-width page-content">

  {%- render 'breadcrumbs' -%}

  <header class="section-header text-center{% if cart.item_count == 0 %} section-header--404{% endif %}">
    <h1 class="section-header__title">{{ 'cart.general.title' | t }}</h1>
    <div class="rte text-spacing">
      {%- if cart.item_count == 0 -%}
        <p>{{ 'cart.general.empty' | t }}</p>
      {%- endif -%}
      <p>{{ 'cart.general.continue_browsing_html' | t: url: routes.all_products_collection_url }}</p>
    </div>
  </header>

  {%- if cart.item_count > 0 -%}
    <form action="{{ routes.cart_url }}" method="post" novalidate data-location="page" id="CartPageForm">
      <div class="cart__page">
        <div data-products class="cart__page-col">
          {% for item in cart.items %}
            {%- render 'cart-item', product: item, sizes: '150px', -%}
          {%- endfor -%}
        </div>
        <div class="cart__page-col">
          {% if settings.cart_notes_enable %}
            <div>
              <label for="CartNote">{{ 'cart.general.note' | t }}</label>
              <textarea name="note" class="input-full cart-notes" id="CartNote">{{ cart.note }}</textarea>
            </div>
          {% endif %}

          <div data-discounts>
            {% if cart.cart_level_discount_applications != blank %}
              <div class="cart__discounts cart__item-sub cart__item-row">
                <div>{{ 'cart.general.discounts' | t }}</div>
                <div class="text-right">
                  {% for cart_discount in cart.cart_level_discount_applications %}
                    <div class="cart__discount">
                      {{ cart_discount.title }} (-{{ cart_discount.total_allocated_amount | money }})
                    </div>
                  {% endfor %}
                </div>
              </div>
            {% endif %}
          </div>

          <div class="cart__item-sub cart__item-row">
            <div>{{ 'cart.general.subtotal' | t }}</div>
            <div data-subtotal>{{ cart.total_price | money }}</div>
          </div>

          {% if settings.cart_terms_conditions_enable %}
            <div class="cart__item-row cart__terms">
              <input type="checkbox" id="CartTerms" class="cart__terms-checkbox">
              <label for="CartTerms">
                {% if settings.cart_terms_conditions_page != blank %}
                  {{ 'cart.general.terms_html' | t: url: settings.cart_terms_conditions_page.url }}
                {% else %}
                  {{ 'cart.general.terms' | t }}
                {% endif %}
              </label>
            </div>
          {% endif %}

          <div class="cart__item-row cart__checkout-wrapper">
            <button type="submit" name="checkout" data-terms-required="{{ settings.cart_terms_conditions_enable }}" class="btn cart__checkout">
              {{ 'cart.general.checkout' | t }}
            </button>

            {% if additional_checkout_buttons and settings.cart_additional_buttons %}
              <div class="additional-checkout-buttons">{{ content_for_additional_checkout_buttons }}</div>
            {% endif %}
          </div>

          <div class="cart__item-row text-center">
            <small>
              {{ 'cart.general.shipping_at_checkout' | t }}<br />
            </small>
          </div>
        </div>
      </div>
    </form>
  {%- endif -%}
</div>

{% schema %}
{
  "name": "t:sections.main-cart.name"
}
{% endschema %}
