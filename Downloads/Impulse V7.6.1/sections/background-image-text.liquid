<div
  data-section-id="{{ section.id }}"
  data-section-type="background-image"
  class="background-media-text background-media-text--{{ section.id }} background-media-text--{{ section.settings.height }} loaded"
  data-aos="background-media-text__animation"
  {% if section.settings.parallax %}data-parallax="true"{% endif %}>

  <div class="background-media-text__container">
    {%- if section.settings.parallax -%}
      <parallax-image class="parallax-container">
        <div class="parallax-image" data-movement="15%" data-parallax-image data-angle="{{ section.settings.parallax_direction }}">
    {%- endif -%}

      {%- if section.settings.image != blank -%}
          {% assign classes = 'image-fit background-media-text__image background-media-text__image--' | append: section.id %}
          {% comment %} Full width image so don't need to adjust sizes attribute, fallback is 100vw {% endcomment %}
          {%- render 'image-element',
            img: section.settings.image,
            img_width: 2400,
            loading: section.settings.lazyload_images,
            classes: classes,
          -%}
      {%- else -%}
        {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
      {%- endif -%}

    {%- if section.settings.parallax -%}
        </div>
      </parallax-image>
    {%- endif -%}
  </div>
  {%- if section.settings.subtitle != blank or section.settings.title != blank or section.settings.text != blank -%}
    <div class="background-media-text__inner">
      <div class="background-media-text__aligner background-media-text--{{ section.settings.layout }}">
        <div class="animation-cropper">
          <div class="animation-contents">
            <div class="background-media-text__text{% if section.settings.framed %} background-media-text__text--framed{% endif %}">
              {%- if section.settings.subtitle -%}
                <p class="h5">{{ section.settings.subtitle }}</p>
              {%- endif -%}
              {%- if section.settings.title != blank -%}
                <p class="h3">{{ section.settings.title | escape }}</p>
              {%- endif -%}
              {%- if section.settings.text != blank -%}
                <div class="rte background-media-text__subtext">{{ section.settings.text }}</div>
              {%- endif -%}
              {%- if section.settings.button_label != blank and section.settings.button_link != blank -%}
                <a href="{{ section.settings.button_link }}" class="btn">
                  {{ section.settings.button_label }}
                </a>
              {%- endif -%}
            </div>
          </div>
        </div>
      </div>
    </div>
  {%- endif -%}
</div>

<div class="background-media-text__spacer background-media-text--{{ section.settings.height }}"></div>

{% schema %}
{
  "name": "t:sections.background-image-text.name",
  "class": "index-section--flush",
  "settings": [
    {
      "type": "text",
      "id": "subtitle",
      "label": "t:sections.background-image-text.settings.subtitle.label",
      "default": "Impressive"
    },
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.background-image-text.settings.title.label",
      "default": "Large image with text box"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "t:sections.background-image-text.settings.text.label",
      "default": "<p>Pair large text with a full-width image to draw attention to an important detail of your brand or product line.</p>"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.background-image-text.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.background-image-text.settings.button_link.label"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.background-image-text.settings.image.label"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.background-image-text.settings.layout.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.background-image-text.settings.layout.options.left.label"
        },
        {
          "value": "right",
          "label": "t:sections.background-image-text.settings.layout.options.right.label"
        }
      ]
    },
    {
      "type": "range",
      "id": "height",
      "label": "t:sections.background-image-text.settings.height.label",
      "default": 550,
      "min": 450,
      "max": 750,
      "step": 100,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "framed",
      "label": "t:sections.background-image-text.settings.framed.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "parallax",
      "label": "t:sections.background-image-text.settings.parallax.label",
      "default": true
    },
    {
      "type": "select",
      "id": "parallax_direction",
      "label": "t:sections.background-image-text.settings.parallax_direction.label",
      "default": "top",
      "options": [
        {
          "value": "top",
          "label": "t:sections.background-image-text.settings.parallax_direction.options.top.label"
        },
        {
          "value": "left",
          "label": "t:sections.background-image-text.settings.parallax_direction.options.left.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "lazyload_images",
      "label": "t:common.lazyload_images.label",
      "info": "t:common.lazyload_images.info",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "t:sections.background-image-text.presets.large_image_with_text_box.name"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
