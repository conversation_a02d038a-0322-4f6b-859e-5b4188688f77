{%- style -%}
  .hero--{{ section.id }} .hero__title {
    font-size: {{ section.settings.title_size | times: 0.5 }}px;
  }
  @media only screen and (min-width: 769px) {
    .hero--{{ section.id }} .hero__title {
      font-size: {{ section.settings.title_size }}px;
    }
  }

  {%- assign button_alpha = section.settings.color_accent | color_extract: 'alpha' -%}
  {% unless button_alpha == 0.0 %}
    .hero--{{ section.id }} .btn {
      background: {{ section.settings.color_accent }} !important;
      border-color: {{ section.settings.color_accent }} !important;

      {%- assign accent_brightness = section.settings.color_accent | color_extract: 'lightness' -%}

      {% if accent_brightness > 40 %}
        color: #000 !important;
      {% endif %}
    }

    {% if settings.button_style == 'angled' %}
      .hero--{{ section.id }} .btn:before,
      .hero--{{ section.id }} .btn:after {
        background: {{ section.settings.color_accent }} !important;
        border-color: {{ section.settings.color_accent }} !important;
      }
    {% endif %}
  {% endunless %}

  {% if section.settings.overlay_opacity > 0 %}
    .hero--{{ section.id }} .hero__text-wrap:after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 3;
      background-color: #000;
      opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};
    }
  {% endif %}
{%- endstyle -%}

<div
  data-section-id="{{ section.id }}"
  data-section-type="video-section"
  class="video-parent-section hero hero--{{ section.id }} hero--{{ section.settings.section_height }} hero--mobile--{{ section.settings.mobile_height }}"
  data-mobile-natural="false"
  data-aos="hero__animation">

  <div class="hero__media hero__media--{{ section.id }}">
    <div class="hero__media-container">

      {%- if section.settings.video_url contains 'youtube.com/watch' -%}
        {%- assign video_id = section.settings.video_url | split: 'v=' -%}
        {%- assign video_id = video_id[1] | split: '&' | first -%}
        <div
          id="YouTubeVideo-{{ section.id }}"
          class="video-div"
          data-type="youtube"
          data-video-id="{{ video_id }}"></div>
      {%- endif -%}

      {%- if section.settings.video_url contains 'youtu.be/' -%}
        {%- assign video_id = section.settings.video_url | split: '.be/' -%}
        {%- assign video_id = video_id[1] | split: '&' | first -%}
        <div
          id="YouTubeVideo-{{ section.id }}"
          class="video-div"
          data-type="youtube"
          data-video-id="{{ video_id }}"></div>
      {%- endif -%}

      {%- if section.settings.video_url contains 'vimeo.com' -%}
        {%- assign video_id = section.settings.video_url | split: '.com/' -%}
        {%- assign video_id = video_id[1] | split: '/' | first -%}
        <div
          id="Vimeo-{{ section.id }}"
          class="video-div"
          data-type="vimeo"
          data-video-id="{{ video_id }}"></div>
      {%- endif -%}

      {%- if section.settings.video_url contains '.mp4' or section.settings.video_url contains '.MP4' -%}
        <video
          id="Mp4Video-{{ section.id }}"
          class="video-div"
          data-type="mp4"
          src="{{ section.settings.video_url }}"
          loop muted playsinline autoplay></video>
      {%- endif -%}

    </div>
  </div>

  <div class="hero__text-wrap">
    <div class="page-width">
      <div class="hero__text-content {{ section.settings.text_align }}">
        {% unless section.settings.title == blank and section.settings.subheading == blank and section.settings.link_text == blank %}
        <div class="hero__text-shadow">
          {%- if section.settings.video_url contains 'vimeo.com' -%}
            <button type="button" id="VimeoTrigger-{{ section.id }}" class="vimeo-mobile-trigger medium-up--hide">
              <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-play" viewBox="18.24 17.35 24.52 28.3"><path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/></svg>
            </button>
          {%- endif -%}

          {%- unless section.settings.title == blank -%}
            <h2 class="h1 hero__title">
              <div class="animation-cropper"><div class="animation-contents">
              {{ section.settings.title | newline_to_br }}
              </div></div>
            </h2>
          {%- endunless -%}
          {%- if section.settings.subheading or section.settings.link -%}
            {%- unless section.settings.subheading == blank -%}
              <div class="hero__subtitle">
                <div class="animation-cropper"><div class="animation-contents">
                  {{ section.settings.subheading | escape }}
                </div></div>
              </div>
            {%- endunless -%}
            {%- if section.settings.link_text != blank -%}
              {%- assign link_href = section.settings.link -%}
              {%- if section.settings.link == blank -%}
                {%- assign link_href = section.settings.video_url -%}
              {%- endif -%}
              <div class="hero__link">
                <div class="animation-cropper">
                  <div class="animation-contents">
                    <a href="{{ link_href }}" class="btn{% if section.settings.color_accent and section.settings.color_accent == 'rgba(0,0,0,0)' %} btn--inverse{% endif %}"
                      {%- if section.settings.video_url contains 'vimeo.com' -%}
                        data-video-id="{{ video_id }}"
                      {%- endif -%}
                    >
                      {%- if link_href contains 'youtube.com/watch' or link_href contains 'youtu.be/' -%}
                        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-play" viewBox="18.24 17.35 24.52 28.3"><path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/></svg>
                      {%- endif -%}
                      {{ section.settings.link_text }}
                    </a>
                  </div>
                </div>
              </div>
            {%- endif -%}
          {%- endif -%}
        </div>
        {% endunless %}
      </div>
    </div>
  </div>

</div>

{% schema %}
{
  "name": "t:sections.hero-video.name",
  "class": "index-section--hero",
  "settings": [
    {
      "type": "textarea",
      "id": "title",
      "label": "t:sections.hero-video.settings.title.label",
      "default": "Bring your\nbrand to life."
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "t:sections.hero-video.settings.title_size.label",
      "default": 80,
      "min": 40,
      "max": 100,
      "unit": "px"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.hero-video.settings.subheading.label",
      "default": "Seamless hero videos"
    },
    {
      "type": "text",
      "id": "link_text",
      "label": "t:sections.hero-video.settings.link_text.label",
      "default": "Optional button"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:sections.hero-video.settings.link.label",
      "info": "t:sections.hero-video.settings.link.info"
    },
    {
      "type": "color",
      "id": "color_accent",
      "label": "t:sections.hero-video.settings.color_accent.label",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "select",
      "id": "text_align",
      "label": "t:sections.hero-video.settings.text_align.label",
      "default": "vertical-center horizontal-center",
      "options": [
        {
          "value": "vertical-center horizontal-left",
          "label": "t:sections.hero-video.settings.text_align.options.vertical-center_horizontal-left.label"
        },
        {
          "value": "vertical-center horizontal-center",
          "label": "t:sections.hero-video.settings.text_align.options.vertical-center_horizontal-center.label"
        },
        {
          "value": "vertical-center horizontal-right",
          "label": "t:sections.hero-video.settings.text_align.options.vertical-center_horizontal-right.label"
        },
        {
          "value": "vertical-bottom horizontal-left",
          "label": "t:sections.hero-video.settings.text_align.options.vertical-bottom_horizontal-left.label"
        },
        {
          "value": "vertical-bottom horizontal-center",
          "label": "t:sections.hero-video.settings.text_align.options.vertical-bottom_horizontal-center.label"
        },
        {
          "value": "vertical-bottom horizontal-right",
          "label": "t:sections.hero-video.settings.text_align.options.vertical-bottom_horizontal-right.label"
        }
      ]
    },
    {
      "type": "text",
      "id": "video_url",
      "label": "t:sections.hero-video.settings.video_url.label",
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "info": "t:sections.hero-video.settings.video_url.info"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "t:sections.hero-video.settings.overlay_opacity.label",
      "info": "t:sections.hero-video.settings.overlay_opacity.info",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "%"
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "t:sections.hero-video.settings.section_height.label",
      "default": "650px",
      "options": [
        {
          "label": "t:sections.hero-video.settings.section_height.options.450px.label",
          "value": "450px"
        },
        {
          "label": "t:sections.hero-video.settings.section_height.options.550px.label",
          "value": "550px"
        },
        {
          "label": "t:sections.hero-video.settings.section_height.options.650px.label",
          "value": "650px"
        },
        {
          "label": "t:sections.hero-video.settings.section_height.options.750px.label",
          "value": "750px"
        },
        {
          "label": "t:sections.hero-video.settings.section_height.options.100vh.label",
          "value": "100vh"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "t:sections.hero-video.settings.mobile_height.label",
      "default": "auto",
      "options": [
        {
          "label": "t:sections.hero-video.settings.mobile_height.options.auto.label",
          "value": "auto"
        },
        {
          "label": "t:sections.hero-video.settings.mobile_height.options.250px.label",
          "value": "250px"
        },
        {
          "label": "t:sections.hero-video.settings.mobile_height.options.300px.label",
          "value": "300px"
        },
        {
          "label": "t:sections.hero-video.settings.mobile_height.options.400px.label",
          "value": "400px"
        },
        {
          "label": "t:sections.hero-video.settings.mobile_height.options.500px.label",
          "value": "500px"
        },
        {
          "label": "t:sections.hero-video.settings.mobile_height.options.100vh.label",
          "value": "100vh"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.hero-video.presets.video_hero.name"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
