<style>
  {%- if settings.color_announcement == settings.color_body_bg -%}
    .announcement-bar {
      border-bottom: 1px solid;
    }
  {%- endif -%}
</style>

{%- render 'announcement-bar', section: section -%}

{% schema %}
{
  "name": "t:sections.header.settings.header_announcement_bar",
  "settings": [
    {
      "type": "checkbox",
      "id": "announcement_compact",
      "label": "t:sections.header.settings.announcement_compact.label"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "announcement",
      "name": "t:sections.header.blocks.announcement.name",
      "limit": 3,
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:sections.header.blocks.announcement.settings.text.label",
          "default": "Hassle-free returns"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "t:sections.header.blocks.announcement.settings.link_text.label",
          "default": "30-day postage paid returns"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.header.blocks.announcement.settings.link.label"
        }
      ]
    }
  ],
  "default": {
    "settings": {}
  },
  "disabled_on": {
    "groups": ["footer", "custom.popups"]
  }
}
{% endschema %}

