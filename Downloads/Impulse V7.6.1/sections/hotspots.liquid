{% assign bgBrightness = section.settings.hotspot_color | brightness_difference: '#fff' %}
{% style %}
  {% for block in section.blocks %}
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 {{ section.settings.hotspot_color | color_modify: 'alpha', 0.5 }}; }
      100% { box-shadow: 0 0 0 10px {{ section.settings.hotspot_color | color_modify: 'alpha', 0 }}; }
    }

    .{{ section.id }} .hotspot__button--{{ block.id }} {
      left: {{ block.settings.horizontal }}%;
      top: {{ block.settings.vertical }}%;
      background-color: {{ section.settings.hotspot_color }};
      animation: pulse 2s infinite;
    }

    .{{ section.id }} .hotspot__button--{{ block.id }}:hover {
      {% if bgBrightness < 125 %}
        background-color: {{ section.settings.hotspot_color | color_darken: 10 }};
      {% else %}
        background-color: {{ section.settings.hotspot_color | color_lighten: 10 }};
      {% endif %}
    }
  {% endfor %}

  .{{ section.id }} .hotspot__button path {
    {% if bgBrightness < 125 %}
      stroke: #000 !important;
    {% else %}
      stroke: #fff !important;
    {% endif %}
  }

  .{{ section.id }} .hotspot__button circle {
    {% if bgBrightness < 125 %}
      fill: #000 !important;
    {% else %}
      fill: #fff !important;
    {% endif %}
  }

  .{{ section.id }} .hotspot__button .icon-plus path {
    {% if bgBrightness < 125 %}
      fill: #000 !important;
      stroke: transparent;
    {% else %}
      fill: #fff !important;
      stroke: transparent;
    {% endif %}
  }
{% endstyle %}

<div class="index-section {{ section.id }} {{ bgBrightness }}">
  {% if section.settings.title != blank %}
    <div class="page-width">
      <h2 class="hotspots__title {{ section.settings.heading_size }} text-{{ section.settings.heading_position }}">{{ section.settings.title }}</h2>
    </div>
  {% endif %}
  <hot-spots class="hotspots-wrapper {% unless section.settings.indent_image %}page-width{% endunless %} {% if section.settings.image_position == 'right' %}is-reverse{% endif %}">

    <div class="hotspots">
      <div class="hotspots__image hotspots__image--indent-{{ section.settings.indent_image }}">
        <div class="grid__image-ratio grid__image-ratio--square">
          {% if section.settings.image != blank %}
            {%- render 'image-element',
              img: section.settings.image,
              img_width: 2400,
              loading: section.settings.lazyload_images,
              sizeVariable: '70vw',
            -%}
          {% else %}
            {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
          {% endif %}
        </div>
      </div>

      <div class="hotspots__buttons">
        {% for block in section.blocks %}
          <button class="hotspot__button hotspot__button--{{ block.id}}" data-button="{{ block.id }}">
            {% assign buttonStyle = section.settings.hotspot_style %}

            {% if buttonStyle == 'dot' %}
              <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-dot" viewBox="0 0 12 12"><circle cx="6" cy="6" r="6" fill="currentColor"/></svg>
            {% elsif buttonStyle == 'plus' %}
              <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-plus" viewBox="0 0 20 20"><path fill="#444" d="M17.409 8.929h-6.695V2.258c0-.566-.506-1.029-1.071-1.029s-1.071.463-1.071 1.029v6.671H1.967C1.401 8.929.938 9.435.938 10s.463 1.071 1.029 1.071h6.605V17.7c0 .566.506 1.029 1.071 1.029s1.071-.463 1.071-1.029v-6.629h6.695c.566 0 1.029-.506 1.029-1.071s-.463-1.071-1.029-1.071z"/></svg>
            {% elsif buttonStyle == 'bag' %}
              <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-bag" viewBox="0 0 64 64"><g fill="none" stroke="#000" stroke-width="2"><path d="M25 26c0-15.79 3.57-20 8-20s8 4.21 8 20"/><path d="M14.74 18h36.51l3.59 36.73h-43.7z"/></g></svg>
            {% elsif buttonStyle == 'tag' %}
              <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-tag" viewBox="0 0 64 64"><path d="m46.69 10.34-10.55.07-25.8 25.8 17.45 17.45 25.8-25.8.07-10.55-6.97-6.97z"/><circle cx="43.95" cy="20.05" r="3.53"/><path d="M14.4 32.15 31.85 49.6"/></svg>
            {% endif %}
          </button>
        {% endfor %}
      </div>
    </div>
    <div class="hotspots__content" data-block-container>
      {% for block in section.blocks %}
        <div class="hotspot-content__block {% if forloop.index0 == 0 %}is-active{% endif %}" data-block-type="{{ block.type }}" data-hotspot-block="{{ block.id }}" {{ block.shopify_attributes }}>
          {% case block.type %}
            {% when 'product' %}
              {%- assign product = block.settings.featured_product -%}
              {% if block.settings.featured_product != blank %}
                {% render 'product-grid-item', product: product, per_row: '1', quick_shop_enable: settings.quick_shop_enable, sizes: '30vw' %}
              {% else %}
                {% render 'onboarding-product-grid-item', i: forloop.index %}
              {% endif %}
            {% when 'paragraph' %}
              {% if block.settings.subheading != blank %}
                <p class="subheading">{{ block.settings.subheading }}</p>
              {% endif %}
              {% if block.settings.heading != blank %}
                <h3>{{ block.settings.heading }}</h3>
              {% endif %}

              {% if block.settings.content != blank %}
                {{ block.settings.content }}
              {% endif %}

              {% if block.settings.button_text != blank %}
                <a href="{{ block.settings.button_link }}" class="btn">{{ block.settings.button_text }}</a>
              {% endif %}
          {% endcase %}
        </div>
      {% endfor %}
    </div>
  </hot-spots>
</div>

{% schema %}
{
  "name": "t:sections.hotspots.name",
  "class": "hotspots-section",
  "max_blocks": 8,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.hotspots.settings.title.label",
      "default": "Shop the look"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.hotspots.settings.heading_size.label",
      "default": "h2",
      "options": [
        {
          "value": "h3",
          "label": "t:sections.hotspots.settings.heading_size.options.small.label"
        },
        {
          "value": "h2",
          "label": "t:sections.hotspots.settings.heading_size.options.medium.label"
        },
        {
          "value": "h1",
          "label": "t:sections.hotspots.settings.heading_size.options.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "heading_position",
      "label": "t:sections.hotspots.settings.heading_position.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.hotspots.settings.heading_position.options.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.hotspots.settings.heading_position.options.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.hotspots.settings.heading_position.options.right.label"
        }
      ]
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.hotspots.settings.image.label",
      "info": "t:sections.hotspots.settings.image.info"
    },
    {
      "type": "checkbox",
      "label": "t:sections.hotspots.settings.indent_image.label",
      "id": "indent_image",
      "default": false
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "t:sections.hotspots.settings.image_position.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.hotspots.settings.image_position.options.left.label"
        },
        {
          "value": "right",
          "label": "t:sections.hotspots.settings.image_position.options.right.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "hotspot_style",
      "label": "t:sections.hotspots.settings.hotspot_style.label",
      "default": "bag",
      "options": [
        {
          "value": "dot",
          "label": "t:sections.hotspots.settings.hotspot_style.options.dot.label"
        },
        {
          "value": "plus",
          "label": "t:sections.hotspots.settings.hotspot_style.options.plus.label"
        },
        {
          "value": "tag",
          "label": "t:sections.hotspots.settings.hotspot_style.options.tag.label"
        },
        {
          "value": "bag",
          "label": "t:sections.hotspots.settings.hotspot_style.options.bag.label"
        }
      ]
    },
    {
      "type": "color",
      "id": "hotspot_color",
      "label": "t:sections.hotspots.settings.hotspot_color.label",
      "default": "#000000"
    },
    {
      "type": "checkbox",
      "id": "lazyload_images",
      "label": "t:common.lazyload_images.label",
      "info": "t:common.lazyload_images.info",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "product",
      "name": "t:sections.hotspots.blocks.product.name",
      "settings": [
        {
          "type": "product",
          "id": "featured_product",
          "label": "t:sections.hotspots.blocks.product.settings.featured_product.label"
        },
        {
          "type": "range",
          "id": "vertical",
          "label": "t:sections.hotspots.blocks.product.settings.vertical.label",
          "default": 50,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "%"
        },
        {
          "type": "range",
          "id": "horizontal",
          "label": "t:sections.hotspots.blocks.product.settings.horizontal.label",
          "default": 50,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "%"
        }
      ]
    },
    {
      "type": "paragraph",
      "name": "t:sections.hotspots.blocks.paragraph.name",
      "settings": [
        {
          "type": "text",
          "id": "subheading",
          "label": "t:sections.hotspots.blocks.paragraph.settings.subheading.label"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.hotspots.blocks.paragraph.settings.heading.label"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.hotspots.blocks.paragraph.settings.content.label"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "t:sections.hotspots.blocks.paragraph.settings.button_text.label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.hotspots.blocks.paragraph.settings.button_link.label"
        },
        {
          "type": "range",
          "id": "vertical",
          "label": "t:sections.hotspots.blocks.paragraph.settings.vertical.label",
          "default": 50,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "%"
        },
        {
          "type": "range",
          "id": "horizontal",
          "label": "t:sections.hotspots.blocks.paragraph.settings.horizontal.label",
          "default": 50,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "%"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.hotspots.name",
      "blocks": [
        {
          "type": "product",
          "settings": {
            "vertical": 25,
            "horizontal": 25
          }
        },
        {
          "type": "product",
          "settings": {
            "vertical": 75,
            "horizontal": 65
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
