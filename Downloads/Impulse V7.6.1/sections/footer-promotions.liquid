{%- liquid
  assign show_section = true
  if template == 'index' and section.settings.hide_homepage
    assign show_section = false
  endif -%
-%}

{%- if show_section -%}
  {%- if section.blocks.size > 0 -%}
    {%- liquid
      assign grid_item_width = 'medium-up--one-half'
      if section.blocks.size == 3
        assign grid_item_width = 'medium-up--one-third'
      endif
    -%}

    <div class="index-section">
      <div class="section--divider">
        <div class="page-width footer-promotions">
          <div class="grid grid--flush-bottom">
            {%- for block in section.blocks -%}
              <div class="grid__item {{ grid_item_width }}" {{ block.shopify_attributes }} data-aos="row-of-3">
                {%- if block.settings.enable_image -%}
                  <a href="{{ block.settings.button_link }}" class="article__grid-image" aria-label="{{ block.settings.title }}">
                    {%- if block.settings.image != blank -%}
                      <div class="image-wrap {% if block.settings.image_mask != 'none' %}svg-mask svg-mask--{{ block.settings.image_mask }}{% endif %}" style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.image.aspect_ratio }}%;">
                        {%- render 'image-element',
                          img: block.settings.image,
                          widths: '180, 360, 540, 720, 900, 1080',
                          sizeVariable: grid_item_width,
                        -%}
                      </div>
                    {%- else -%}
                      <div class="image-wrap {% if block.settings.image_mask != 'none' %}svg-mask svg-mask--{{ block.settings.image_mask }}{% endif %}">
                        {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
                      </div>
                    {%- endif -%}
                  </a>
                {%- endif -%}
                {%- if block.settings.title != blank -%}
                  <h2 class="h3">{{ block.settings.title }}</h2>
                {%- endif -%}
                {%- if block.settings.text != blank -%}
                  <div class="rte-setting text-spacing">{{ block.settings.text }}</div>
                {%- endif -%}
                {%- if block.settings.button_label != blank -%}
                  <a href="{{ block.settings.button_link }}" class="btn btn--secondary btn--small">
                    {{ block.settings.button_label }}
                  </a>
                {%- endif -%}
              </div>
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  {%- endif -%}
{%- endif -%}

{% schema %}
{
  "name": "t:sections.footer-promotions.name",
  "max_blocks": 3,
  "class": "index-section--footer",
  "settings": [
    {
      "type": "checkbox",
      "id": "hide_homepage",
      "label": "t:sections.footer-promotions.settings.hide_homepage.label"
    }
  ],
  "blocks": [
    {
      "type": "promotion",
      "name": "t:sections.footer-promotions.blocks.column.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "enable_image",
          "label": "t:sections.footer-promotions.blocks.column.settings.enable_image.label",
          "default": true
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.footer-promotions.blocks.column.settings.image.label"
        },
        {
          "type": "select",
          "id": "image_mask",
          "label": "t:common.image_mask.label",
          "default": "none",
          "options": [
            {
              "value": "none",
              "label": "t:common.image_mask.options.none.label"
            },
            {
              "value": "portrait",
              "label": "t:common.image_mask.options.portrait.label"
            },
            {
              "value": "landscape",
              "label": "t:common.image_mask.options.landscape.label"
            },
            {
              "value": "square",
              "label": "t:common.image_mask.options.square.label"
            },
            {
              "value": "rounded",
              "label": "t:common.image_mask.options.rounded.label"
            },
            {
              "value": "rounded-wave",
              "label": "t:common.image_mask.options.rounded-wave.label"
            },
            {
              "value": "rounded-top",
              "label": "t:common.image_mask.options.rounded-top.label"
            },
            {
              "value": "star",
              "label": "t:common.image_mask.options.star.label"
            },
            {
              "value": "splat-1",
              "label": "t:common.image_mask.options.splat-1.label"
            },
            {
              "value": "splat-2",
              "label": "t:common.image_mask.options.splat-2.label"
            },
            {
              "value": "splat-3",
              "label": "t:common.image_mask.options.splat-3.label"
            },
            {
              "value": "splat-4",
              "label": "t:common.image_mask.options.splat-4.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.footer-promotions.blocks.column.settings.title.label",
          "default": "Site-wide promotion"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "t:sections.footer-promotions.blocks.column.settings.text.label",
          "default": "<p>Use this section to promote content throughout every page of your site. Add images for further impact.</p>"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "t:sections.footer-promotions.blocks.column.settings.button_label.label",
          "default": "Optional button"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.footer-promotions.blocks.column.settings.button_link.label"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "promotion"
      },
      {
        "type": "promotion"
      },
      {
        "type": "promotion"
      }
    ]
  },
  "disabled_on": {
    "groups": ["header", "custom.popups"]
  }
}
{% endschema %}
