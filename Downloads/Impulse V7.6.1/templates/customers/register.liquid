<div class="page-width page-width--tiny page-content">
  <header class="section-header">
    <h1 class="section-header__title">{{ 'customer.register.title' | t }}</h1>
  </header>

  <div class="form-vertical">
    {%- form 'create_customer' -%}

      {{ form.errors | default_errors }}

      <label for="FirstName">{{ 'customer.register.first_name' | t }}</label>
      <input type="text" name="customer[first_name]" id="FirstName" class="input-full"{% if form.first_name %} value="{{ form.first_name }}"{% endif %} autocapitalize="words" autofocus>

      <label for="LastName">{{ 'customer.register.last_name' | t }}</label>
      <input type="text" name="customer[last_name]" id="LastName" class="input-full"{% if form.last_name %} value="{{ form.last_name }}"{% endif %} autocapitalize="words">

      <label for="Email">{{ 'customer.register.email' | t }}</label>
      <input type="email" name="customer[email]" id="Email" class="input-full{% if form.errors contains 'email' %} error{% endif %}"{% if form.email %} value="{{ form.email }}"{% endif %} autocorrect="off" autocapitalize="off">

      <label for="CreatePassword">{{ 'customer.register.password' | t }}</label>
      <input type="password" name="customer[password]" id="CreatePassword" class="input-full{% if form.errors contains 'password' %} error{% endif %}">

      <p>
        <label for="register-submit" class="hidden-label">{{ 'customer.register.submit' | t }}</label>
        <input type="submit" id="register-submit" value="{{ 'customer.register.submit' | t }}" class="btn btn--full">
      </p>

    {%- endform -%}
  </div>
</div>
